<?php

namespace App\Services\Home;

use App\Repositories\Main\VIPTypeRepository;
use App\Repositories\Home\ProductRepository;
use App\Repositories\Home\ProductPriceSearchRepository;
use App\Repositories\Home\TypeinfoRepository;
use App\Services\CommonService;
use Illuminate\Support\Facades\Lang;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\ProductHelpler;
use App\Repositories\Main\OrderformRepository;

class ProductService
{
    public function __construct(
        private ProductRepository $productRepository,
        private ProductPriceSearchRepository $productPriceSearchRepository,
        private TypeinfoRepository $typeinfoRepository,
        private VIPTypeRepository $vipTypeRepository,
        private OrderformRepository $orderformRepository,
    ) {}

    /**
     * 取得價格搜尋選項
     *
     * @param int $distributor_id: 經銷商ID
     * @return void
     */
    public function getProductPriceSearchs($distributor_id = 0)
    {
        $productPriceSearchs = $this->productPriceSearchRepository->getProductPriceSearchs($distributor_id);
        return CommonService::objectToArray($productPriceSearchs);
    }

    /**
     * 取得分館商品資料（含 webtype_keywords, webtype_description）
     * @param int|null $id
     * @param int|null $searchPrev
     * @return array|null
     */
    public function getProductLayerByIdOrSearchPrev($id = null, $searchPrev = null)
    {
        $product = $this->productRepository->getProductLayerByIdOrSearchPrev($id, $searchPrev);
        return CommonService::objectToArray($product);
    }

    /**
     * 製作麵包屑
     * @param array $layer
     * @return array
     */
    public function getTitleArray($layer)
    {
        $layer = (array)$layer;
        if (!isset($layer['branch_id'])) {
            throw new \Exception(Lang::get('查無此頁'));
        } else {
            $branch_id = $layer['branch_id'];
        }
        if (!isset($layer['prev_id'])) {
            throw new \Exception(Lang::get('查無此頁'));
        } else {
            $prev_id = $layer['prev_id'];
        }

        $title_array = [];
        while ($branch_id != 0) {
            $type_info = $this->getTypeinfo($branch_id);
            array_unshift($title_array, [
                'id' => $type_info['id'],
                'title' => $type_info['title'],
                'type' => 'typeinfo'
            ]);
            $branch_id = $type_info['branch_id'];
        }

        $product = $this->productRepository->getProductById($prev_id);
        $product = CommonService::objectToArray($product);

        array_unshift($title_array, [
            'id' => $product['id'],
            'title' => $product['title'],
            'type' => 'product'
        ]);

        if (config('control.control_platform') == 1) {
            $MemberInstance = new MemberInstance($product['distributor_id']);
            $user_data = $MemberInstance->get_user_data();
            if ($user_data && $user_data['user_type'] == 1) {
                $shop_name = $user_data['shop_name'] ? $user_data['shop_name'] : $user_data['name'];
                array_unshift($title_array, [
                    "id" => $user_data['id'],
                    "title" => $shop_name,
                    "type" => 'distributor',
                ]);
            }
        }
        return $title_array;
    }

    /**
     * 商品搜尋與標記
     * @param array $search_cond
     * @param array $coupon_button
     * @param array $act_button
     * @param int $frontend_user_id
     * @return array
     */
    public function getSearchProduct($search_cond = [], $coupon_button = [], $act_button = [], $frontend_user_id = 0)
    {
        $search_cond['online'] = 1;
        $search_cond['frontend_view'] = true;
        $search_cond['frontend_user_id'] = $frontend_user_id;
        $search_cond['sort_method'] = request()->get('sort') ?? '';
        $search_cond['price_range_search'] = request()->get('price_range') ?? '';
        $search_result = ProductHelpler::search_product($search_cond);
        if (empty($search_result['productinfo']->items()) == false) {
            foreach ($search_result['productinfo']->items() as $obj) {
                $obj->pic1 = json_decode($obj->pic, true)[0];
                $obj->show = ProductHelpler::get_product_price_option(CommonService::objectToArray($obj));
                $coupon_button[$obj->id] = ProductHelpler::find_prod_coupon($obj->id);
                $act_button[$obj->id]['act_data'] = ProductHelpler::find_prod_act($obj->id);
            }
        }

        return [
            'sort' => $search_result['sort_method'],
            'price_range' => $search_result['price_range_search'],
            'rowCount' => $search_result['rowCount'],
            'rowData' => $search_result['productinfo'],
            'coupon_button' => $coupon_button,
            'act_button' => $act_button,
        ];
    }

    /**
     * 取得指定 parent_id 的子分類
     * @param int $parent_id
     * @return array
     */
    public function getSubProductByParentId($parent_id)
    {
        $subProduct = $this->typeinfoRepository->getSubTypeinfoByParentId($parent_id);
        return CommonService::objectToArray($subProduct);
    }

    /**
     * 取得隨機推薦分館
     * @param int $rid
     * @return array
     */
    public function getRandADV($rid)
    {
        $randAdv = $this->productRepository->getRandADV($rid);
        return CommonService::objectToArray($randAdv);
    }

    /**
     * 取得分類資訊
     * @param int $id
     * @return array
     * @throws \Exception
     */
    public function getTypeinfo($id)
    {
        if ($id) {
            if (!is_numeric($id)) {
                throw new \Exception(Lang::get('查無此頁'));
            }
        }
        $typeinfo = $this->typeinfoRepository->getTypeinfoById($id);
        if ($typeinfo == null) {
            throw new \Exception(Lang::get('查無此頁'));
        }
        $typeinfo = CommonService::objectToArray($typeinfo);

        if (!$typeinfo) throw new \Exception(Lang::get('查無此頁'));
        if ($typeinfo['online'] == 2) throw new \Exception(Lang::get('查無此頁'));
        return $typeinfo;
    }

    /**
     * 計算最高折扣金額
     * 計算方式：
     *      1. 將cv額度翻成台幣 * 折扣比率(要轉換成百分比)
     *      2. 小數點無條件捨去
     * @param float $priceCV: 商品CV金額
     * @param float $vipDiscountRatio: Vip折扣比率
     * @return int
     */
    public function calculateMaxDiscount($priceCV, $vipDiscountRatio)
    {
        $exchangeRate = config('extra.skychakra.exchange_rate_set')['NT'] ?? 1;
        $TWCV = $priceCV * $exchangeRate;
        return intval(floor($TWCV * $vipDiscountRatio / 100));
    }
    /**
     * 判斷是否為首次購買
     * @param int $userId
     * @param int $vipId
     * @return bool
     */
    public function isFirstBuy($userId, $vipId)
    {
        if ($vipId == 0) {
            $count = $this->orderformRepository->getOrderCountByUserId($userId);
            return $count == 0;
        }
        return false;
    }

    /**
     * 取得最高折扣金額（含首次購買邏輯）
     * @param int $userId
     * @param int $vipId
     * @param float $priceCV
     * @return int
     */
    public function getMaxDiscountWithFirstBuy($userId, $vipId, $priceCV)
    {
        $firstBuy = $this->isFirstBuy($userId, $vipId);

        $maxDiscount = 0;
        // vip 0 有首購優惠，所以購買過就不適用優惠
        if ($vipId > 0 || $firstBuy) {
            $vipDiscountRatio = $this->vipTypeRepository->getVipDiscountRatio($vipId);
            $maxDiscount = $this->calculateMaxDiscount(floatval($priceCV), floatval($vipDiscountRatio));
        }
        return $maxDiscount;
    }
}
