<?php

namespace App\Http\Controllers\order;

use App\Http\Controllers\MainController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\Admin\Order\RollbackPointbackService;

class SimpleOrderDeleteCtrl extends MainController
{
    public function __construct(private RollbackPointbackService $rollbackService)
    {
        parent::__construct();
    }

    /**
     * 顯示簡化的訂單刪除頁面
     */
    public function index()
    {
        return view('order.simple_delete.index');
    }

    /**
     * 根據訂單ID批量刪除
     */
    public function deleteByIds(Request $request)
    {
        $orderIds = $request->input('order_ids');
        
        if (empty($orderIds)) {
            return response()->json(['success' => false, 'message' => '請輸入訂單ID']);
        }

        // 將輸入的ID字符串轉換為陣列
        $idsArray = array_filter(array_map('trim', explode(',', $orderIds)));
        
        if (empty($idsArray)) {
            return response()->json(['success' => false, 'message' => '請輸入有效的訂單ID']);
        }

        try {
            $deletedCount = 0;
            $errors = [];

            foreach ($idsArray as $orderId) {
                if (!is_numeric($orderId)) {
                    $errors[] = "無效的訂單ID: {$orderId}";
                    continue;
                }

                $order = DB::connection('main_db')->table('orderform')->where('id', $orderId)->first();
                
                if (!$order) {
                    $errors[] = "找不到訂單ID: {$orderId}";
                    continue;
                }

                // 如果訂單有回饋記錄，先回滾
                if ($order->do_award_time != '') {
                    $this->rollbackService->rollbackPointback([$orderId]);
                }

                // 刪除訂單
                DB::connection('main_db')->table('orderform')->where('id', $orderId)->delete();
                $deletedCount++;

                Log::info('Manual order deletion', [
                    'order_id' => $orderId,
                    'create_time' => $order->create_time,
                    'user_id' => $order->user_id,
                    'total' => $order->total
                ]);
            }

            $message = "成功刪除 {$deletedCount} 筆訂單";
            if (!empty($errors)) {
                $message .= "，錯誤：" . implode(', ', $errors);
            }

            return response()->json([
                'success' => true, 
                'message' => $message,
                'deleted_count' => $deletedCount,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            Log::error('Order deletion error', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => '刪除失敗：' . $e->getMessage()]);
        }
    }

    /**
     * 根據條件搜尋並刪除訂單
     */
    public function deleteByConditions(Request $request)
    {
        $conditions = $request->only([
            'user_number', 'create_time_start', 'create_time_end', 
            'transport_location_name', 'transport_location_phone', 
            'transport_email', 'total_min', 'total_max'
        ]);

        // 移除空值
        $conditions = array_filter($conditions, function($value) {
            return $value !== null && $value !== '';
        });

        if (empty($conditions)) {
            return response()->json(['success' => false, 'message' => '請至少輸入一個搜尋條件']);
        }

        try {
            $query = DB::connection('main_db')->table('orderform');

            // 根據會員編號查詢
            if (!empty($conditions['user_number'])) {
                $user = DB::connection('main_db')->table('account')
                    ->where('number', $conditions['user_number'])
                    ->first();
                if (!$user) {
                    return response()->json(['success' => false, 'message' => '找不到該會員編號']);
                }
                $query->where('user_id', $user->id);
            }

            // 日期範圍
            if (!empty($conditions['create_time_start'])) {
                $query->where('create_time', '>=', strtotime($conditions['create_time_start']));
            }
            if (!empty($conditions['create_time_end'])) {
                $query->where('create_time', '<=', strtotime($conditions['create_time_end'] . ' 23:59:59'));
            }

            // 其他條件
            if (!empty($conditions['transport_location_name'])) {
                $query->where('transport_location_name', 'like', '%' . $conditions['transport_location_name'] . '%');
            }
            if (!empty($conditions['transport_location_phone'])) {
                $query->where('transport_location_phone', $conditions['transport_location_phone']);
            }
            if (!empty($conditions['transport_email'])) {
                $query->where('transport_email', $conditions['transport_email']);
            }
            if (!empty($conditions['total_min'])) {
                $query->where('total', '>=', $conditions['total_min']);
            }
            if (!empty($conditions['total_max'])) {
                $query->where('total', '<=', $conditions['total_max']);
            }

            // 先查詢符合條件的訂單
            $orders = $query->get();

            if ($orders->isEmpty()) {
                return response()->json(['success' => false, 'message' => '找不到符合條件的訂單']);
            }

            // 確認刪除
            $orderInfo = $orders->map(function($order) {
                return "ID: {$order->id}, 時間: " . date('Y-m-d H:i:s', $order->create_time) . ", 金額: {$order->total}";
            })->take(5)->toArray();

            if ($orders->count() > 5) {
                $orderInfo[] = "... 還有 " . ($orders->count() - 5) . " 筆訂單";
            }

            return response()->json([
                'success' => true,
                'preview' => true,
                'message' => "找到 {$orders->count()} 筆符合條件的訂單",
                'orders' => $orderInfo,
                'order_ids' => $orders->pluck('id')->toArray()
            ]);

        } catch (\Exception $e) {
            Log::error('Order search error', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => '搜尋失敗：' . $e->getMessage()]);
        }
    }

    /**
     * 確認刪除預覽的訂單
     */
    public function confirmDelete(Request $request)
    {
        $orderIds = $request->input('order_ids', []);
        
        if (empty($orderIds)) {
            return response()->json(['success' => false, 'message' => '沒有要刪除的訂單']);
        }

        try {
            $deletedCount = 0;

            foreach ($orderIds as $orderId) {
                $order = DB::connection('main_db')->table('orderform')->where('id', $orderId)->first();
                
                if (!$order) {
                    continue;
                }

                // 如果訂單有回饋記錄，先回滾
                if ($order->do_award_time != '') {
                    $this->rollbackService->rollbackPointback([$orderId]);
                }

                // 刪除訂單
                DB::connection('main_db')->table('orderform')->where('id', $orderId)->delete();
                $deletedCount++;

                Log::info('Batch order deletion', [
                    'order_id' => $orderId,
                    'create_time' => $order->create_time,
                    'user_id' => $order->user_id,
                    'total' => $order->total
                ]);
            }

            return response()->json([
                'success' => true, 
                'message' => "成功刪除 {$deletedCount} 筆訂單"
            ]);

        } catch (\Exception $e) {
            Log::error('Batch order deletion error', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => '刪除失敗：' . $e->getMessage()]);
        }
    }
}
