<?php

namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

// Repositories
use App\Repositories\Main\AccountRepository;
use App\Repositories\Main\CenterRepository;
use App\Repositories\Main\CenterStaffRepository;
use App\Repositories\Main\CenterLevelRepository;
use App\Repositories\Main\CenterRoleRepository;

// Services
use App\Services\Center\CenterService;
use App\Services\Center\CenterStaffService;

// Transformer
use App\Transformers\CenterTransformer;

/**
 * 中心管理Controller
 */
class Center extends MainController
{

    const PER_PAGE_ROWS = 20;

    public function __construct(
        protected AccountRepository $accountRepository,
        protected CenterRepository $centerRepository,
        protected CenterStaffRepository $centerStaffRepository,
        protected CenterLevelRepository $centerLevelRepository,
        protected CenterRoleRepository $centerRoleRepository,
        protected CenterService $centerService,
        protected CenterStaffService $centerStaffService,
        protected CenterTransformer $centerTransformer
    ) {
        parent::__construct();
    }

    /**
     * 顯示中心列表頁面
     */
    public function index(Request $request)
    {
        // 取得篩選參數
        $filters = [
            'name' => $request->get('name'),
            'center_level_id' => $request->get('center_level_id'),
            'status' => $request->get('status')
        ];

        // 取得中心列表
        $centers = $this->centerRepository->getCenterWithFilterAndPaginate($filters, self::PER_PAGE_ROWS);

        // 取得中心等級選項
        $centerLevels = $this->centerLevelRepository->getCenterLevels();

        $this->data['centers'] = $centers;
        $this->data['center_levels'] = $centerLevels;
        $this->data['filters'] = $filters;

        return view('admin.center.index', ['data' => $this->data]);
    }

    /**
     * 建立中心
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:64|min:2',
            'center_level_id' => 'required|integer|exists:main_db.center_level,id',
            'status' => 'sometimes|integer|in:0,1'
        ]);

        $this->centerRepository->create($validatedData);

        return redirect('/order/center/index')->with('success', '中心建立成功');
    }

    /**
     * 顯示新增中心表單
     */
    public function create()
    {
        // 取得中心等級選項
        $centerLevels = $this->centerLevelRepository->getCenterLevels();

        // 取得角色選項
        $centerRoles = $this->centerRoleRepository->getCenterRoles();

        $this->data['center_levels'] = $centerLevels;
        $this->data['center_roles'] = $centerRoles;

        return view('admin.center.create_simple', ['data' => $this->data]);
    }

    /**
     * 顯示編輯中心表單
     */
    public function edit(Request $request)
    {
        $id = $request->get('id');

        if (!$id) {
            $this->error('參數錯誤');
        }

        $center = $this->centerRepository->getCenterWithStaff($id);

        if (!$center) {
            $this->error('中心不存在');
        }

        $this->data['center'] = $center;
        $this->data['center_levels'] = $this->centerLevelRepository->getCenterLevels();
        $this->data['center_roles'] = $this->centerRoleRepository->getCenterRoles();

        return view('admin.center.edit', ['data' => $this->data]);
    }

    /**
     * 處理更新中心請求
     */
    public function update(Request $request)
    {
        $validatedData = $request->validate([
            'id' => 'required|integer|exists:main_db.centers,id',
            'name' => 'required|string|max:64',
            'center_level_id' => 'required|integer|exists:main_db.center_level,id',
            'status' => 'nullable|in:0,1'
        ]);

        $validatedData['status'] = $validatedData['status'] ?? 1;

        $center = $this->centerService->updateCenter($validatedData['id'], $validatedData);
        $respData = $this->centerTransformer->transformCenterData($center);

        return response()->json([
            'status' => true,
            'message' => '中心更新成功',
            'data' => $respData,
            'redirect_url' => '/admin/center/edit?id=' . $validatedData['id']
        ]);
    }

    /**
     * 刪除中心
     */
    public function destroy(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:main_db.centers,id'
        ]);

        try {
            $this->centerService->deleteCenter($request->id);

            return response()->json([
                'status' => true,
                'message' => '中心刪除成功'
            ]);
        } catch (\RuntimeException $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 409);
        } catch (\Exception $e) {
            Log::error('刪除中心失敗', [
                'center_id' => $request->id,
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'status' => false,
                'message' => '刪除失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 取得現役人員概覽
     */
    public function getActiveStaffOverview(Request $request)
    {
        try {
            $centerId = $request->get('center_id');

            if (!$centerId) {
                return response()->json([
                    'status' => false,
                    'message' => '中心ID為必填項目'
                ], 400);
            }

            // 獲取現役人員資料
            $activeStaff = $this->centerStaffRepository->getActiveStaffByCenterId($centerId);
            $respData = $this->centerTransformer->transformRoleGroups($activeStaff);

            return response()->json([
                'status' => true,
                'message' => '成功取得現役人員概覽',
                'data' => $respData
            ], 200);
        } catch (\Exception $e) {
            Log::error('取得現役人員概覽失敗', [
                'center_id' => $request->get('center_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => false,
                'message' => '系統錯誤，請稍後再試'
            ], 500);
        }
    }

    /**
     * AJAX 會員編號自動完成
     */
    public function getAccountSuggestions(Request $request)
    {
        $validatedData = $request->validate([
            'query' => 'required|string|min:1|max:100',
            'limit' => 'nullable|integer|min:1|max:100'
        ]);

        $accounts = $this->accountRepository->searchByNumberOrName(
            $validatedData['query'],
            $validatedData['limit'] ?? 10
        );

        $formattedAccounts = $this->centerTransformer->transformAccountSuggestions($accounts);

        return response()->json([
            'status' => true,
            'data' => $formattedAccounts,
            'count' => $formattedAccounts->count(),
            'query' => $validatedData['query'],
            'limit' => $validatedData['limit'] ?? 10
        ]);
    }

    /**
     * 指派角色
     */
    public function assignRole(Request $request)
    {
        $validatedData = $request->validate([
            'center_id' => 'required|integer|exists:main_db.centers,id',
            'account_number' => 'required|string|exists:main_db.account,number',
            'role_code' => 'required|string|in:regional_center,founder,director,executive_director,lecturer,market,sales',
            'start_at' => 'nullable|date',
            'profit_percentage' => 'nullable|numeric|min:0|max:100'
        ]);

        $result = $this->centerStaffService->assignRoleByAccountNumber(
            $validatedData['center_id'],
            $validatedData['account_number'],
            $validatedData['role_code'],
            $validatedData['start_at'] ?? null,
            $validatedData['profit_percentage'] ?? 0.0
        );

        return response()->json([
            'status' => true,
            'message' => '角色指派成功',
            'data' => $result
        ]);
    }

    /**
     * 更新分潤百分比
     */
    public function updateProfitPercentage(Request $request)
    {
        $validatedData = $request->validate([
            'staff_id' => 'required|integer|exists:main_db.center_staff,id',
            'profit_percentage' => 'required|numeric|min:0|max:100'
        ]);

        try {
            $this->centerStaffService->updateProfitPercentage(
                $validatedData['staff_id'],
                $validatedData['profit_percentage']
            );

            return response()->json([
                'status' => true,
                'message' => '分潤百分比更新成功'
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 400);
        } catch (\RuntimeException $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * 移除角色
     */
    public function removeRole(Request $request)
    {
        $validatedData = $request->validate([
            'center_id' => 'required|integer|exists:main_db.centers,id',
            'account_id' => 'required|integer|exists:main_db.account,id',
            'role_code' => 'required|string|in:regional_center,founder,director,executive_director,lecturer,market,sales'
        ]);

        $result = $this->centerStaffService->removeRole(
            $validatedData['center_id'],
            $validatedData['account_id'],
            $validatedData['role_code']
        );

        return response()->json([
            'status' => true,
            'message' => '角色移除成功',
            'data' => $result
        ]);
    }

    /**
     * 檢查中心是否可以刪除
     */
    public function canDelete($id)
    {
        $activeStaffCount = $this->centerStaffRepository->getActiveStaffCountByCenterId($id);

        if ($activeStaffCount > 0) {
            return response()->json([
                'status' => false,
                'message' => "無法刪除：中心仍有 {$activeStaffCount} 位現役人員"
            ], 409);
        }

        return response()->json([
            'status' => true,
            'message' => '可以刪除此中心'
        ]);
    }

    /**
     * 切換中心狀態
     */
    public function toggleStatus(Request $request)
    {
        $validatedData = $request->validate([
            'id' => 'required|integer|exists:main_db.centers,id',
            'status' => 'required|integer|in:0,1'
        ]);

        $this->centerRepository->updateStatus($validatedData['id'], $validatedData['status']);

        return response()->json([
            'status' => true,
            'message' => '操作成功'
        ]);
    }

    /**
     * 批量更新狀態
     */
    public function batchStatus(Request $request)
    {
        $validatedData = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:main_db.centers,id',
            'status' => 'required|integer|in:0,1'
        ]);

        $this->centerRepository->batchUpdateStatus($validatedData['ids'], $validatedData['status']);

        return response()->json([
            'status' => true,
            'message' => '操作成功'
        ]);
    }

    /**
     * 批量刪除中心
     */
    public function batchDelete(Request $request)
    {
        $validatedData = $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:main_db.centers,id'
        ]);

        $result = $this->centerService->batchDeleteCenters($validatedData['ids']);

        return response()->json([
            'status' => true,
            'message' => $result['message'],
            'data' => $result['data']
        ]);
    }

    /**
     * 批量指派角色
     */
    public function batchAssignRole(Request $request)
    {
        $validatedData = $request->validate([
            'center_id' => 'required|integer|exists:main_db.centers,id',
            'account_ids' => 'required|array|min:1',
            'account_ids.*' => 'integer|exists:main_db.account,id',
            'role_code' => 'required|string|in:regional_center,founder,director,executive_director,lecturer,market,sales',
            'start_at' => 'nullable|date'
        ]);

        $result = $this->centerStaffService->batchAssignRole(
            $validatedData['center_id'],
            $validatedData['account_ids'],
            $validatedData['role_code'],
            $validatedData['start_at'] ?? null
        );

        return response()->json([
            'status' => true,
            'message' => '批量角色指派完成',
            'data' => $result
        ]);
    }

    /**
     * 批量移除角色
     */
    public function batchRemoveRole(Request $request)
    {
        $validatedData = $request->validate([
            'center_id' => 'required|integer|exists:main_db.centers,id',
            'account_ids' => 'required|array|min:1',
            'account_ids.*' => 'integer|exists:main_db.account,id',
            'role_code' => 'required|string|in:regional_center,founder,director,executive_director,lecturer,market,sales',
            'end_at' => 'nullable|date'
        ]);

        $result = $this->centerStaffService->batchRemoveRole(
            $validatedData['center_id'],
            $validatedData['account_ids'],
            $validatedData['role_code'],
            $validatedData['end_at'] ?? null
        );

        return response()->json([
            'status' => true,
            'message' => '批量角色移除完成',
            'data' => $result
        ]);
    }

    /**
     * 驗證角色指派
     */
    public function validateRoleAssignment(Request $request)
    {
        $validatedData = $request->validate([
            'center_id' => 'required|integer|exists:main_db.centers,id',
            'account_id' => 'required|integer|exists:main_db.account,id',
            'role_code' => 'required|string|in:regional_center,founder,director,executive_director,lecturer,market,sales'
        ]);

        $result = $this->centerStaffService->validateRoleAssignment(
            $validatedData['center_id'],
            $validatedData['account_id'],
            $validatedData['role_code']
        );

        return response()->json([
            'status' => true,
            'data' => $result
        ]);
    }

    /**
     * 取得中心角色狀態
     */
    public function getRoleStatus(Request $request)
    {
        $validatedData = $request->validate([
            'center_id' => 'required|integer|exists:main_db.centers,id',
            'role_codes' => 'nullable|array',
            'role_codes.*' => 'string|in:regional_center,founder,director,executive_director,lecturer,market,sales'
        ]);

        $result = $this->centerStaffService->getRoleStatus(
            $validatedData['center_id'],
            $validatedData['role_codes'] ?? []
        );

        return response()->json([
            'status' => true,
            'data' => $result
        ]);
    }
}
