<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
//Photonic Class
use App\Services\pattern\MemberInstance;
use App\Services\pattern\TaskHelper;
use App\Services\ReCaptcha\ReCaptcha;

class Login extends PublicController {

  public static $ACCOUNT_MODE = ['審核中', '通過', '黑名單', '停用'];
  public function __construct() {
    parent::__construct(request());
    self::$ACCOUNT_MODE = [
      Lang::get('審核中'), 
      Lang::get('通過'), 
      Lang::get('黑名單'), 
      Lang::get('停用')
    ]; 
  }

  public function login() {
    $login_redirect = request()->get('jumpUri') ?? url('Member/member');
    $this->data['login_redirect'] = $login_redirect;
    return view('home.login.login',['data'=>$this->data]);
  }

  /*一般登入*/
  public function pagelogin() {
    $account = request()->post('account');
    $password = request()->post('password');
    $redirect = request()->post('redirect');

    $rule = [
      'account'  => 'required',
      'password' => 'required'
    ];
    $msg = [
      'account.required' => Lang::get('帳號不得為空'),
      'password.required' => Lang::get('密碼不得為空'),
    ];
    $data = [
      'account'  => $account,
      'password' => $password
    ];

    $validate = Validator::make($data, $rule, $msg);

    if ($validate->fails()) {
      $this->error($validate->errors()->first());
    }

    $MemberInstance = new MemberInstance(0);
    $adminData = $MemberInstance->get_user_data($addr_change="ori", ['a.'.MemberInstance::$account_column => $account]);
    if($adminData){
      if($adminData['pwd'] == md5($password)){
        if($adminData['status'] == '0' || $adminData['status'] == '3'){
          $this->error(Lang::get('帳號為審核中或停用，無法登入'));
        }
        $has_login_bonus = $this->do_login($adminData);
        $login_msg = $has_login_bonus ? Lang::get('完成每日登入') : Lang::get('操作成功');

        // 檢查其個資是否已填寫完成，若無則優先跳轉到個人資料頁面  (這段加了又註解，因為客人反反覆覆，先留著可能要再用到)
        // if(empty($adminData['name']) || empty($adminData['email']) || empty($adminData['tele'])){
        //   $this->success('請先填寫個人資料', url('index/member/member'));
        // } else{

          if($redirect = request()->get('redirect')){

            return redirect($redirect);
          }else{
            $redirect=url('Member/member');

            return redirect($redirect);
          }
        // }

      }else{
        $this->error(Lang::get('帳號或密碼錯誤'));
      }
    }else{
      $this->error(Lang::get('帳號或密碼錯誤'));
    }
  }
  private function do_login($user_data){
    session()->put('user', $user_data);
    //session()->put('cart','[]');
    return TaskHelper::send_bonus_3($user_data['id']); /*登入獎勵*/
  }

  /*登出*/
  public function logout() {
    session()->forget('user');
    // session()->forget('cart');
    $this->redirect(url('/'));
  }

  /*註冊畫面*/
  public function signup() {
    // $this->error('天脈確認舊會員資料中，暫不開放註冊');

    $city = DB::table('city')->get();
    $this->data['city'] = CommonService::objectToArray($city);

    $consent = DB::table('consent')->first();
    $this->data['consent'] = CommonService::objectToArray($consent);

    $recommend = request()->get('recommend');
    if(!$recommend){
      $recommend = session()->get('productinfo_recommend');
    }
    $recommend_user = DB::connection('main_db')->table('account')->where('number', $recommend)->first();
    if(!$recommend_user){
      $ruser = request()->get('ruser');
      $recommend_user = DB::connection('main_db')->table('account')->where('id', $ruser)->first();
    }
    $recommend_user = CommonService::objectToArray($recommend_user);
    if(!$recommend_user){
      $recommend_user = [
        'share_title' =>'',
        'share_text' =>'',
        'share_pic' =>'',
        'number' =>'',
        'recommend_content' =>'',
      ];
    }
    $this->data['recommend_user'] = $recommend_user;

    $recommend_user_number = $recommend_user ? $recommend_user['number'] : "";
    $this->data['recommend_user_number'] = $recommend_user_number;

    /*商品瀏覽設定*/
    $product_view = DB::connection('main_db')->table('product_view')->where('online', 1)->orderBy('id', 'desc')->get();
    $this->data['product_view'] = CommonService::objectToArray($product_view);

    return view('home.login.signup',['data'=>$this->data]);
  }
  /*建立註冊資料*/
  public function dosignup() {

    $newData = request()->post();
    unset($newData['_token']);

    /*檢查輸入資料*/
    $MemberInstance = new MemberInstance(0);
    $returnData = $MemberInstance->arrange_data_to_db_format($newData);

    if($returnData['code']==0){
      $this->error($returnData['msg']);
    }

    /*新增會員*/
    $newData['from_order'] = 0;
    $newData['user_type'] = 1; /*預設為審核通過*/
    $newData['status'] = 1; /*預設為審核通過*/
    $returnData = $MemberInstance->insert_user_data($newData);
    if($returnData['code']==0){ $this->error($returnData['msg']); }
    TaskHelper::send_bonus_1($returnData['data']['id']); /*註冊獎勵*/
    TaskHelper::send_bonus_2($returnData['data']['id']); /*推薦獎勵*/

    return redirect(url('Login/login'));
  }

  public function order_create_account(){
    $postData = request()->post();
    $newData['name'] = $postData['transport_location_name'] ?? '';
    $newData['email'] = $postData['transport_email'] ?? '';
    $newData['phone'] = $postData['transport_location_phone'] ?? '';
    $newData['tele'] = $postData['transport_location_tele'] ?? '';
    $newData['home'] = $postData['addrC'] ?? '';
    $newData['upline_user'] = $postData['upline_user'] ?? '';
    $newData['status'] = 1;

    /*檢查輸入資料*/
    $MemberInstance = new MemberInstance(0);
    $returnData = $MemberInstance->insert_user_data($newData);
    if($returnData['code']==0){ $this->error($returnData['msg']); }
    TaskHelper::send_bonus_1($returnData['data']['id']); /*註冊獎勵*/
    TaskHelper::send_bonus_2($returnData['data']['id']); /*推薦獎勵*/

    $this->success(Lang::get('操作成功'));
  }

  /*激活帳號*/
  public function signcheck() {
    $name = request()->get('id');
    $code = request()->get('code');
    if($code!=md5($name.'photonic')){
      $this->error(Lang::get('連結有誤'), url('Index/index'));
    }
    if(DB::connection('main_db')->table('account')->where(MemberInstance::$account_column, $name)->update(['status' => '1'])){
      $this->success(Lang::get('操作成功'), url('Index/index'));
    }else{
      $this->error(Lang::get('連結有誤或已完成驗證'), url('Index/index'));
    }
  }

  /*忘記密碼信寄送結果頁面*/
  public function forgot_form(){
    $request = request();

    if (env('APP_ENV') === 'production') {
      if (config('extra.shop.google_recaptcha_sitekey')) {
        // _GOOGLE_RECAPTCHA_SEC_KEY 就是 google 給的 Secret Key
        $google_recaptcha_seckey = config('extra.shop.google_recaptcha_seckey');
        if($google_recaptcha_seckey){
          $recaptcha = new ReCaptcha($google_recaptcha_seckey);
          $gRecaptchaResponse = $request->post('g-recaptcha-response');
          $remoteIp = request()->server('REMOTE_ADDR');
          $resp = $recaptcha->verify($gRecaptchaResponse, $remoteIp);
          if(!$resp->isSuccess()){
            $this->error(Lang::get('請先證明您不是機器人'));
          }
        }
      }
    }

    $account = $request->post('account_forget');
    $now_email = $request->post('email_forget');
    $MemberInstance = new MemberInstance(0);
    $adminData = $MemberInstance->get_user_data($addr_change="ori", ['a.'.MemberInstance::$account_column => $account]);

    // 使用者確實存在才可以寄出重置信
    if(!empty($adminData)){
      // 忘記密碼時輸入的信箱為優先。
      $adminData['email'] = $now_email;
      $this->send_user_forgot_password_email($adminData);
    }
    $this->data['adminData'] = $adminData;
    return view('home.login.forgot_form',['data'=>$this->data]);
  }
  /*寄送忘記密碼信*/
  private function send_user_forgot_password_email($user_data){
    $time = time();
    $code = base64_encode(base64_encode($user_data[MemberInstance::$account_column]).base64_encode($time));
    $MemberInstance = new MemberInstance($user_data['id']);
    $MemberInstance->update_user_data(['f_code' => $code,]);

    $globalMailData = parent::getMailData();
    $forget_password_letter = Lang::get('menu.密碼重設信消費者');
    $forget_password_letter = str_replace("{name}", $user_data['name'], $forget_password_letter);
    $forget_password_letter = str_replace("{date_time}", date("Y-m-d H:i:s"), $forget_password_letter);
    $forget_password_letter = str_replace("{mailFromName}", $globalMailData['mailFromName'], $forget_password_letter);
    $change_password_url = url('Login/check_forgot')."?id=".base64_encode($user_data[MemberInstance::$account_column])."&asef=".base64_encode($time);
    $forget_password_letter = str_replace("{change_password_url}", $change_password_url, $forget_password_letter);
    $mailBody = "
    <html>
      <head>
        <style>
          table, th{
            border: 1px solid #c7c5c5;
            border-collapse: collapse;
            background-color: #fffbed;
          }
          td{
            border: 0px solid black;
            border-collapse: collapse;
            vertical-align:middle;
          }
        </style>
      </head>
      <body>
        ".$forget_password_letter."
        <center></center>
        <div>
          ". $globalMailData['system_email']['forget_password'] ."
        </div>
        <div style='color:red;'>
          ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
        </div>
      </body>
    </html>
    ";
    $mail_return = parent::Mail_Send($mailBody,'client',$user_data['email'],Lang::get('密碼重設通知'));
    return $mail_return;
  }
  /*檢查忘記密碼是否還可使用*/
  public function check_forgot(){
    $id = request()->get('id');
    $asef_en = request()->get('asef');
    $asef = base64_decode($asef_en);

    $now_time=(time()-$asef)/60;
    if($id=='' or $now_time > 30){
      $this->error(Lang::get('連結有誤'),url('Index/index'));
    }

    $this->data['id'] = base64_decode($id);
    $this->data['code'] = base64_encode($id.$asef_en);

    return view('home.login.check_forgot',['data'=>$this->data]);
  }
  /*透過忘記密碼信更改密碼*/
  public function change_forgot(){
    $id = request()->post('id');
    $code = request()->post('code');

    $MemberInstance = new MemberInstance(0);
    $adminData = $MemberInstance->get_user_data($addr_change="ori", ['a.'.MemberInstance::$account_column => $id]);
    if(!empty($adminData)){
      if(!$adminData['f_code'] || $adminData['f_code'] != $code){
        $this->error(Lang::get('連結有誤'));
      }

      $updateData = [
        'password' => request()->post('password'),
        'passwordB' => request()->post('passwordB'),
        'f_code' => null,
      ];
      $MemberInstance->change_user_id($adminData['id']);
      $returnData = $MemberInstance->update_user_data($updateData);
      if($returnData['code'] == 0){ $this->error($returnData['msg']); }

      $this->success(Lang::get('操作成功'), url('Index/index'));
    }else{
      $this->error(Lang::get('資料有誤'));
    }
  }

  /*隱私政策畫面*/
  public function privacy_rule() {
    $consent = DB::table('consent')->first();
    $this->data['consent'] = CommonService::objectToArray($consent);
    return view('home.login.privacy_rule',['data'=>$this->data]);
  }

  /*google 登入/註冊、綁定商城帳號*/
  public function g_access_token(){
    return view('home.login.g_access_token',['data'=>$this->data]);
  }
  public function g_login(){
    // Get and decode the POST data
    header("Content-Type: application/json; charset=UTF-8");
    $userEmail = request()->post('email') ?? '';
    $userName = request()->post('name') ?? '';
    $open = request()->post('open') ?? '';
    if($userEmail=='' || $userName=='' || $open==''){
      $this->error(Lang::get('授權失敗'));
    }

    $count = MemberInstance::getMemberNumber();

    $first_login_url = "";
    if(!empty($userEmail) && $open!=1){ /*做註冊/登入*/
      if(empty(DB::connection('main_db')->table('account')->where("gmail", $userEmail)->first())){
        $newData = [
          'name' => $userName,
          //'email' => $email,
          'gmail' => $userEmail,
          'phone' => '',
          'birthday' => '',
          'home' => '',
          'status' => '1',
          'createtime' => time(),
          'number' => config('extra.shop.subDeparment') . 'US' . date('Ymd') . $count
        ];
        //dump($newData);
        $new_user_id = DB::connection('main_db')->table('account')->insertGetId($newData);
        TaskHelper::send_bonus_1($new_user_id); /*註冊獎勵*/
        TaskHelper::send_bonus_2($new_user_id); /*推薦獎勵*/
        $first_login_url = url('Member/member');
      }

      $adminData = DB::connection('main_db')->table('account')->where("gmail", $userEmail)->first();
      $adminData = CommonService::objectToArray($adminData);
      if($adminData){
        if($adminData['status'] == '0' || $adminData['status'] == '3'){
          $this->error(Lang::get('帳號為審核中或停用，無法登入'));
        }
        $this->do_login($adminData);
        $this->success($first_login_url);
      }else{
        $this->error(Lang::get('帳號或密碼錯誤'));
      }
      //$this->success('登入成功', url('Index/index'));
    }
    else{ //做開通
      $first_login_url = url('Member/member');
      $user_new = DB::connection('main_db')->table('account')->where("gmail", $userEmail)->first();
      $user_new = CommonService::objectToArray($user_new);
      if(!$user_new){ //都沒有，直接加
        DB::connection('main_db')->table('account')->where("email", $this->user['email'])->update(['gmail' => $userEmail]);
        $this->success($first_login_url);
      }
      else if($user_new['email']!=""){ //有帳號，但商城帳號不為空
        $this->error(Lang::get('此帳戶已綁定商城帳號'));
      }
      else if($user_new['email']==""){ //有帳號，但商城帳號為空，資料合併
        $this->bind_account($user_new, 'gmail', $this->user['email']);
        $this->success($first_login_url);
      }
      $this->error(Lang::get('授權失敗'));
    }
  }

  /*facebook 登入*/
  public function fb_login(){
    // Get and decode the POST data
    header("Content-Type: application/json; charset=UTF-8");
    $userEmail = request()->post('U3');
    $userName = request()->post('ig');
    if(!empty($userEmail)){
      $first_login_url = "";
      if(empty(DB::connection('main_db')->table('account')->where("FB_id", $userEmail)->first())){
        $count = MemberInstance::getMemberNumber();
        $newData = [
          'name' => $userName,
          //'email' => $email,
          'FB_id' => $userEmail,
          'phone' => '',
          'birthday' => '',
          'home' => '',
          'status' => '1',
          'createtime' => time(),
          'number' => config('extra.shop.subDeparment') . 'US' . date('Ymd') . $count
        ];
        //dump($newData);
        $new_user_id = DB::connection('main_db')->table('account')->insertGetId($newData);
        TaskHelper::send_bonus_1($new_user_id); /*註冊獎勵*/
        TaskHelper::send_bonus_2($new_user_id); /*推薦獎勵*/
        $first_login_url = url('Member/member');
      }

      $adminData = DB::connection('main_db')->table('account')->where("FB_id", $userEmail)->first();
      $adminData = CommonService::objectToArray($adminData);
      if($adminData){
        if($adminData['status'] == '0' || $adminData['status'] == '3'){
          $this->error(Lang::get('帳號為審核中或停用，無法登入'));
        }
        $this->do_login($adminData);
        $this->success($first_login_url);
      }else{
        $this->error(Lang::get('帳號或密碼錯誤'));
      }								
    }	
  }
  /*商城帳號綁定 facebook*/
  public function fb_open(){
    $userEmail = request()->post('U3');
    $userName = request()->post('ig');

    $user_new = DB::connection('main_db')->table('account')->where("FB_id", $userEmail)->first();
    $user_new = CommonService::objectToArray($user_new);
    if(!$user_new){ //都沒有，直接加
      DB::connection('main_db')->table('account')->where("email", $this->user['email'])->update(['FB_id' => $userEmail]);
      $this->success(url('Member/member'));
    }
    else if( $user_new['email']!="" ){ //有帳號，但商城帳號不為空
      $this->error(Lang::get('此帳戶已綁定商城帳號'));
    }
    else if( $user_new['email']=="" ){ //有帳號，但商城帳號為空，資料合併
      $this->bind_account($user_new, 'FB_id', $this->user['email']);
      $this->success('');
    }
    $this->error(Lang::get('授權失敗'));
  }

  /*line 登入*/
  public function line_login(){
    // Get and decode the POST data
    header("Content-Type: application/json; charset=UTF-8");
    $userEmail = request()->post('U3');
    $userName = request()->post('ig');
    if(!empty($userEmail)){
      $first_login_url = "";
      if(empty(DB::connection('main_db')->table('account')->where("line_id", $userEmail)->first())){
        $count = MemberInstance::getMemberNumber();
        $newData = [
          'name' => $userName,
          //'email' => $email,
          'line_id' => $userEmail,
          'phone' => '',
          'birthday' => '',
          'home' => '',
          'status' => '1',
          'createtime' => time(),
          'number' => config('extra.shop.subDeparment') . 'US' . date('Ymd') . $count
        ];
        //dump($newData);
        $new_user_id = DB::connection('main_db')->table('account')->insertGetId($newData);
        TaskHelper::send_bonus_1($new_user_id); /*註冊獎勵*/
        TaskHelper::send_bonus_2($new_user_id); /*推薦獎勵*/
        $first_login_url = url('Member/member');
      }

      $adminData = DB::connection('main_db')->table('account')->where("line_id", $userEmail)->first();
      $adminData = CommonService::objectToArray($adminData);
      if($adminData){
        if($adminData['status'] == '0' || $adminData['status'] == '3'){
          $this->error(Lang::get('帳號為審核中或停用，無法登入'));
        }
        $this->do_login($adminData);
        $this->success($first_login_url);
      }else{
        $this->error(Lang::get('帳號或密碼錯誤'));
      }
    }
    $this->error(Lang::get('授權失敗'));
  }
  /*商城帳號綁定 line*/
  public function line_open(){
    $userEmail = request()->post('U3');
    $userName = request()->post('ig');

    $user_new = DB::connection('main_db')->table('account')->where("line_id", $userEmail)->first();
    $user_new = CommonService::objectToArray($user_new);
    if(!$user_new){ //都沒有，直接加
      DB::connection('main_db')->table('account')->where("email", $this->user['email'])->update(['line_id' => $userEmail]);
      $this->success(url('Member/member'));
    }
    else if( $user_new['email']!="" ){ //有帳號，但商城帳號不為空
      $this->error(Lang::get('此帳戶已綁定商城帳號'));
    }
    else if( $user_new['email']=="" ){ //有帳號，但商城帳號為空，資料合併
      $this->bind_account($user_new, 'line_id', $this->user['email']);
      $this->success('');
    }
    $this->error(Lang::get('授權失敗'));
  }

  /*社群帳號綁定(合併) 商城帳號*/
  public function account_open(){
    $id = request()->post('id'); /*目標商城帳號*/
    $pw = request()->post('pw');	/*目標商城帳號密碼*/
    $user_new = request()->post('user_new'); /*社群帳號*/
    $aim = request()->post('aim'); 			/*社群登入方式*/

    if(empty($id)){
      $this->error(Lang::get('帳號不得為空'));
    }

    $aa = DB::connection('main_db')->table('account')->where("email", $id)->first();
    $aa = CommonService::objectToArray($aa);
    if(empty($aa)){
      $this->error(Lang::get('帳號或密碼錯誤'));
    }
    if($aa['pwd'] != md5($pw)){
      $this->error(Lang::get('帳號或密碼錯誤'));
    }

    if($aa[$aim] != ''){
      $this->error(Lang::get('此帳戶已綁定社群帳號'));
    }

    $user_new = DB::connection('main_db')->table('account')->where($aim, $user_new)->first();
    $user_new = CommonService::objectToArray($user_new);
    if($user_new[MemberInstance::$account_column]!=""){
      $this->error(Lang::get('此帳戶已綁定商城帳號'));
    }

    $this->bind_account($user_new, $aim, $id);
    $this->success('');
  }
  private function bind_account($user_new, $social_column, $account_column){
    $aa = DB::connection('main_db')->table('account')->where("email", $account_column)->first();
    $aa = CommonService::objectToArray($aa);

    DB::table('coupon_pool')->where("owner", $user_new['id'])->update(['owner' =>$aa['id']]); //優待券合併
    DB::table('excel')->where("account_number", $user_new['id'])->update(['account_number' =>$aa['id']]); //商品註冊
    DB::table('contact_find_prod')->where("user_id", $user_new['id'])->update(['user_id' =>$aa['id']]); //找貨回函
    DB::table('examinee_info')->where("user_id", $user_new['id'])->update(['user_id' =>$aa['id']]); //報名資料
    DB::table('consumption_draw_record')->where("user_id", $user_new['id'])->update(['user_id' =>$aa['id']]); //刮刮卡紀錄
    DB::table('consumption_exchange_record')->where("user_id", $user_new['id'])->update(['user_id' =>$aa['id']]); //累積消費領取紀錄
    DB::table('consumption_pay_record')->where("user_id", $user_new['id'])->update(['user_id' =>$aa['id']]); //付款紀錄

    DB::connection('main_db')->table('orderform')->where("user_id", $user_new['id'])->update(['user_id' =>$aa['id']]); //訂單合併
    DB::connection('main_db')->table('points_record')->where("user_id", $user_new['id'])->update(['user_id' =>$aa['id']]); //點數紀錄合併

    /*合併帳號*/
    DB::connection('main_db')->table('account')->where("email", $aa['email'])//合併
    ->update([
      'point' => $aa['point']+$user_new['point'], /*合併點數*/
      'total' => $aa['total']+$user_new['total'], /*合併訂單金額總計*/
      'ordernum' => $aa['ordernum']+$user_new['ordernum'], /*合併訂單數量*/
      $social_column => $user_new[$social_column], /*添加社群帳號*/
    ]);
    DB::connection('main_db')->table('account')->delete($user_new['id']);//合併完刪除
  }


  public function town_ajax(){
    if(!empty($city = DB::table('town')->whereRaw("CNo = ?",request()->post("CNo"))->get())){
      $city = CommonService::objectToArray($city);
      foreach( $city as $k => $v){
        echo "<option value='".$v['AutoNo']."'>".$v['Name']."</option>";
      }
    }else{
      echo "<option value=''>".Lang::get('請選擇鄉鎮區')."</option>";
    }
  }
  public function zip_ajax(){	
    if(!empty($town = DB::table('town')->whereRaw("AutoNo = '".request()->post("TNo")."'")->first())){
      $town = CommonService::objectToArray($town);
      echo $town['Post'];
    }else{
      echo Lang::get('無資料');
    }
  }
  public function city_town_ajax() {
    $post_data = request()->post();

    try {
      if (empty($post_data['city']) == true) {
        throw new \Exception(Lang::get('資料有誤'));
      }

      if (empty($post_data['town']) == true) {
        throw new \Exception(Lang::get('資料有誤'));
      }

      $city = $post_data['city'];
      $town = $post_data['town'];

      $result = Db::table('city')->join('town', 'city.AutoNo', '=', 'town.CNo')
      ->where('city.AutoNo', $city)
      ->where('town.AutoNo', $town)
      ->select(['city.Name as city', 'town.Name as town'])
      ->first();

      if (empty($result) == true) {
        throw new \Exception(Lang::get('資料有誤'));
      }

      $city_town = $result->city . $result->town;
    }
    catch (\Exception $e) {
      return ['code' => 401, 'result' => $e->getMessage()];
    }

    return ['code' => 200, 'result' => $city_town];
    }
}

