<?php

namespace app\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//Photonic Class
use App\Http\Controllers\home\PublicController;
use App\Services\pattern\MemberInstance;
use App\Services\CommonService;

class Ajax extends PublicController
{
    /*取得優惠專區、活動專區選單*/
    public function offerNavMenu(Request $request)
    {
        /*優惠專區選單*/
        $act = DB::table('act')->where('online', 1)->get();
        $act = CommonService::objectToArray($act);

        if ($act) {
            foreach ($act as $actKey => $actValue) {
                $act[$actKey]['title'] = $actValue['name'];
            }
        }

        $offerNavMenu['offerMenu'] = $act;

        /*活動專區選單*/
        $onlineact = DB::table('experience')->where('online', 1)->get();
        $onlineact = CommonService::objectToArray($onlineact);

        if ($onlineact) {
            foreach ($onlineact as $actKey => $actValue) {
                $onlineact[$actKey]['title'] = $actValue['title'];
            }
        }

        $offerNavMenu['activityMenu'] = $onlineact;

        return $offerNavMenu;
    }

    /*取得「有顯示的」當階及下第一層分類(依傳入的id, action)*/
    public function productMenu(Request $request)
    {
        return parent::getProductMenu($request->post('action'), $request->post('id'), 0, $request->post('category_type'));
    }

    /*左側商品選單*/
    public function getProdAsideMenu(Request $request)
    {
        $action = $request->post('action', '');
        $id = $request->post('id', '');
        $category_type = $request->post('category_type', null);

        $productMenu = [];
        $typeinfo_columns = ['title', 'id', 'pic'];

        /*位於分類，且有提供分類id*/
        // if ($action == 'typeinfo' && $id != '') {
        //     $typeinfo = DB::table('typeinfo')->find($id);
        //     $typeinfo = CommonService::objectToArray($typeinfo);

        //     if ($typeinfo) {
        //         /*找出同階的分類*/
        //         $productMenu = DB::table('typeinfo')->select($typeinfo_columns)
        //             ->where('distributor_id', $typeinfo['distributor_id'])
        //             ->where('parent_id', $typeinfo['parent_id'])
        //             ->where('branch_id', $typeinfo['branch_id'])
        //             ->where('online', 1)
        //             ->where(function ($query) {
        //                 $query->where('end', '<=', 0)
        //                     ->orWhere(function ($query2) {
        //                         $query2->where('start', '<', time())
        //                             ->where('end', '>', time());
        //                     });
        //             })
        //             ->orderBy('order_id')
        //             ->get();
        //         $productMenu = CommonService::objectToArray($productMenu);

        //         foreach ($productMenu as $key => $vo) {
        //             if ($id == $vo['id']) {
        //                 $productMenu[$key]['show'] = 'show';
        //             }

        //             $productMenu[$key]['action'] = 'typeinfo';

        //             $subType = DB::table('typeinfo')->select($typeinfo_columns)
        //                 ->where('distributor_id', $typeinfo['distributor_id'])
        //                 ->where('branch_id', $vo['id'])
        //                 ->where('online', 1)
        //                 ->where(function ($query) {
        //                     $query->where('end', '<=', 0)
        //                         ->orWhere(function ($query2) {
        //                             $query2->where('start', '<', time())
        //                                 ->where('end', '>', time());
        //                         });
        //                 })
        //                 ->orderBy('order_id')
        //                 ->get();
        //             $subType = CommonService::objectToArray($subType);

        //             foreach ($subType as $key2 => $vo2) {
        //                 if (mb_strlen($subType[$key2]['title'], 'utf8') > 15) {
        //                     $subType[$key2]['title'] = mb_substr($subType[$key2]['title'], 0, 15, 'utf8') . '…';
        //                 }
        //             }

        //             $productMenu[$key]['subType'] = $subType;
        //         }
        //     }
        // }

        /*到這都還沒設定productMenu，視為位於其他地方(ex:分館)*/
        if ($productMenu == []) {
            $productMenu = DB::table('product')->select(['title', 'id', 'pic_icon as pic', 'distributor_id'])->where('online', 1);

            if (config('control.control_platform') == 0) {
                $productMenu = $productMenu->where('distributor_id', 0);
            } else {
                if ($action == 'distributor') {
                    if ($id != '0') {
                        $MemberInstance = new MemberInstance($id);
                        $user_data = $MemberInstance->get_user_data();

                        if ($user_data) {
                            if ($user_data['user_type'] == 1) {
                                $productMenu = $productMenu->where('distributor_id', $id);
                            }
                        }
                    }
                } else if ($action == 'product' && $id != '') {
                    $product = DB::table('product')->where('id', $id)->first();
                    $product = CommonService::objectToArray($product);

                    if (empty($product) == false) {
                        $productMenu = $productMenu->where('distributor_id', $product['distributor_id']);
                    }
                }
            }

            // 如果指定了 category_type，需要篩選只包含該類型商品的分館
            if ($category_type !== null) {
                $productMenu = $productMenu->whereExists(function ($query) use ($category_type) {
                    $query->select(DB::raw(1))
                        ->from('productinfo')
                        ->where('productinfo.final_array', 'LIKE', "CONCAT('%\"prev_id\":\"', product.id, '\"%')")
                        ->where('productinfo.category_type', $category_type)
                        ->where('productinfo.online', 1);
                });
            }

            $productMenu = $productMenu->orderBy('order_id')->orderBy('id')->get();
            $productMenu = CommonService::objectToArray($productMenu);

            foreach ($productMenu as $key => $vo) {
                if ($id == $vo['id']) {
                    $productMenu[$key]['show'] = 'show';
                }

                $productMenu[$key]['action'] = 'product';
                $subType = DB::table('typeinfo')->select($typeinfo_columns)
                    ->where('distributor_id', $vo['distributor_id'])
                    ->where('parent_id', $vo['id'])
                    ->where('branch_id', 0)
                    ->where('online', 1)
                    ->where(function ($query) {
                        $query->where('end', '<=', 0)
                            ->orWhere(function ($query2) {
                                $query2->where('start', '<', time())
                                    ->where('end', '>', time());
                            });
                    })
                    ->orderByRaw('typeinfo.order_id')
                    ->get();
                $subType = CommonService::objectToArray($subType);

                foreach ($subType as $key2 => $vo2) {
                    if (mb_strlen($subType[$key2]["title"], 'utf8') > 15) {
                        $subType[$key2]["title"] = mb_substr($subType[$key2]["title"], 0, 15, 'utf8') . '…';
                    }
                }

                $productMenu[$key]['subType'] = $subType;
            }
        }

        return $productMenu;
    }

    /*取得最新消息資料*/
    public function newslink(Request $request)
    {
        $outputData = [
            'online' => intval(DB::table('index_online')->where('id', 1)->value('block_news')),
            'news' => [],
        ];

        if ($outputData['online'] > 0) {
            $db_result = DB::table('news')->select(['id', 'title', 'time'])->where('online', 1)->orderBy('orders')->orderBy('time', 'desc')->limit(3)->get();
            $db_result = CommonService::objectToArray($db_result);

            if (empty($db_result) == false) {
                foreach ($db_result as $key => $value) {
                    $db_result[$key]['time'] = date('Y-m-d', strtotime($value['time']));
                }

                $outputData['news'] = $db_result;
            }
        }

        return $outputData;
    }

    public function chpwd(Request $request)
    {
        $MemberInstance = new MemberInstance($request->post('id'));
        $data = $MemberInstance->get_user_data();

        if ($data['pwd'] == md5($request->post('pwd'))) {
            return ['status' => true];
        } else {
            return ['status' => false];
        }
    }

    public function ckaccount(Request $request)
    {
        $MemberInstance = new MemberInstance(0);
        $adminData = $MemberInstance->get_user_data('ori', ['a.' . MemberInstance::$account_column =>  $request->post(MemberInstance::$account_column)]);

        ob_clean();

        if ($adminData) {
            echo json_encode(false); // 用戶名已存在
        } else {
            echo json_encode(true); // 用戶帳號可用
        }
    }

    /*商品問答功能*/
    public function prodAllQa(Request $request)
    {
        $cond = [
            ['site_name', '=', config('extra.shop.subDeparment') . '_sub'],
            ['prod_id', '=', $request->post('prodInfoId')],
        ];

        $resData = DB::connection('main_db')->table('product_qa')->where($cond)->orderBy('prod_qa_id', 'desc')->get();
        $resData = CommonService::objectToArray($resData);

        foreach ($resData as $key => $value) {
            $resData[$key]['q_datetime'] = $value['q_datetime'] ?: date('Y-m-d', strtotime($value['q_datetime']));
            $resData[$key]['a_datetime'] = $value['a_datetime'] ?: date('Y-m-d', strtotime($value['a_datetime']));
        }

        $this->success($resData);
    }

    public function prodQaCreate(Request $request)
    {
        $prod_q = $request->post('prodQ');

        if (!$prod_q) {
            $this->error(Lang::get('資料不完整'));
        }

        $siteName = config('extra.shop.subDeparment') . '_sub';
        $uid = session('user.id', 0);
        $prod_id = $request->post('prodInfoId');
        $q_datetime = date('Y-m-d H:i:s');

        if ($uid == 0) {
            $this->error(Lang::get('請先登入會員'));
        }

        /*處理distributor_id*/
        if (config('control.control_platform') == 0) {
            $distributor_id = 0;
        } else {
            $distributor_id = intval(DB::table('productinfo')->where('id', $prod_id)->value('distributor_id'));
        }

        DB::connection('main_db')->table('product_qa')->insert([
            'distributor_id' => $distributor_id,
            'uid' => $uid,
            'prod_id' => $prod_id,
            'prod_q' => str_replace("\n", '<br>', $prod_q),
            'q_datetime' => $q_datetime,
            'site_name' => $siteName,
            'prod_addr' => env('APP_URL') . 'Product/productinfo' . '?' . http_build_query(['id' => $prod_id]),
        ]);

        $subject = Lang::get('商品問答');
        $product_qa_letter = Lang::get('商品問答信管理者');
        $product_qa_letter = str_replace("{prod_q}", $prod_q, $product_qa_letter);
        $mailBody = '<html><head></head><body><div>';
        $mailBody .= $product_qa_letter;
        $mailBody .= '</div>';
        $mailBody .= '<div style="color:red;">';
        $mailBody .= '≡ ' . Lang::get('此信件為系統自動發送，請勿直接回覆') . ' ≡';
        $mailBody .= '</div></body></html>';

        if ($distributor_id == 0) {
            parent::Mail_Send($mailBody, 'admin', '', $subject);
        } else {
            $MemberInstance = new MemberInstance($distributor_id);
            $user_data = $MemberInstance->get_user_data_distributor();

            if ($user_data) {
                parent::Mail_Send($mailBody, 'client', $user_data['email'], $subject);
            }
        }

        $this->success('發送成功');
    }

    /*關閉側邊廣告*/
    public function closeAdSide(Request $request)
    {
        session()->put('closeAdSide', 'true');
    }

    /*商品加入/取消人氣*/
    public function love_record(Request $request)
    {
        return $this->deal_record($request, 'product_love');
    }

    /*商品加入/取消我的收藏*/
    public function store_record(Request $request)
    {
        return $this->deal_record($request, 'product_store');
    }

    /*依給定資料表處理紀錄*/
    private function deal_record($request, $tableName)
    {
        $prodInfoId = $request->post('prodInfoId', null);

        if (!$prodInfoId) {
            $this->error(Lang::get('資料不完整'));
        }

        $status = $request->post('status', null);
        if ($status === null) {
            $this->error(Lang::get('資料不完整'));
        }

        $user_id = session()->get('user.id');

        if (!$user_id) {
            $this->error(Lang::get('請先登入會員'));
        }

        if ($status == 0) { /*取消*/
            DB::table($tableName)->where('product_id', $prodInfoId)->where('user_id', $user_id)->delete();
        } else { /*加入*/
            $has_loved = DB::table($tableName)->where('product_id', $prodInfoId)->where('user_id', $user_id)->get();
            $has_loved = CommonService::objectToArray($has_loved);

            if (empty($has_loved) == true) {
                DB::table($tableName)->insert([
                    'user_id' => $user_id,
                    'product_id' => $prodInfoId,
                ]);
            }
        }

        $this->success(DB::table($tableName)->where('product_id', $prodInfoId)->count());
    }

    /*展示icon*/
    public function icon(Request $request)
    {
        return view('home.ajax.icon', ['data' => $this->data]);
    }
}
