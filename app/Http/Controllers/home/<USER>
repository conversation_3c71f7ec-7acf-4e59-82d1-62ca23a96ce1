<?php

namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
//Photonic Class
use App\Services\CommonService;
use App\Services\pattern\ExaminationHelper;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;
use App\Services\pattern\recursiveCorrdination\cartRC\MemberFactory;
use App\Services\pattern\recursiveCorrdination\discountRC\Proposal as DiscountProposal;
use App\Services\pattern\recursiveCorrdination\discountRC\MemberFactory as DiscountMemberFactory;
use App\Services\pattern\recursiveCorrdination\discountRC\ActCalculate;

use App\Http\Controllers\home\Tspg;
use App\Http\Controllers\home\LinePay;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\AskpriceHelper;
use App\Services\pattern\ProductHelpler;
use App\Services\pattern\Invoice;
use App\Services\pattern\OrderHelper;
use App\Http\Controllers\admin\CartMethod;

class Cart extends PublicController
{
    public function __construct()
    {
        parent::__construct(request()->instance());

        header('Set-Cookie: cross-site-cookie=name; SameSite=None; Secure');
        header('Set-Cookie: cookie2=name; SameSite=None; Secure', false);

        if ($this->user['id'] == 0) {
            $this->error(Lang::get('請先登入會員'), url('Login/login'));
        };
    }

    public function buy_askprice()
    {
        if (isset(config('control.close_function_current')['詢價回函'])) {
            $this->error(Lang::get('查無此頁'));
        }
        if ($this->user["id"] == 0) {
            $this->error(Lang::get('請先登入會員'), url('Login/login'));
        };

        $user_id = $this->user["id"];
        $AskpriceHelper = new AskpriceHelper();
        $result = $AskpriceHelper->getOne('a.user_id ="' . $user_id . '"');
        $main = $result['main'];
        if (!$main) {
            $this->error(Lang::get('資料有誤'));
        }
        $main_record = $result['current'];
        if (!$main_record) {
            $this->error(Lang::get('資料有誤'));
        }
        if ($main_record['status'] != 1) {
            $this->error(Lang::get('此詢價尚未被回覆'));
        }
        if ($main_record['agree'] != 1) {
            $this->error(Lang::get('此詢價未被同意'));
        }
        if ($main_record['expired_date']) {
            if (strtotime($main_record['expired_date'] . ' +1Day') < time()) {
                $this->error(Lang::get('已過購買期限'));
            }
        }
        if ($main_record['bought'] == 1) {
            $this->error(Lang::get('已完成此詢價的購買'));
        }

        session()->forget('cart');
        session()->put('cart', json_encode([
            $main['product_type_id'] . '_askprice' . $main['id'] => $main_record['num'],
        ]));
        $this->redirect(url('Cart/cart'));
    }

    public function choose_shop()
    {
        /* 處理購物車商品 start */
        $Proposal = OrderHelper::get_Proposal_GetCartData('cart_all', $this->user["id"]);

        if (config('control.control_platform') == 1) { /*有啟用平台*/
            $cartData_shop = [];
            $MemberInstance = new MemberInstance(0);

            foreach ($Proposal->getCartArray() as $key => $value) {
                $singleData = Proposal::get_singleData($key, 'online'); /* 取得商品資料 */
                $singleData['num'] = $value;
                $singleData['key'] = $key;

                if (!isset($cartData_shop[$singleData['distributor_id']])) {
                    $MemberInstance->change_user_id($singleData['distributor_id']);
                    $cartData_shop[$singleData['distributor_id']] = [
                        'shop' => $MemberInstance->get_user_data_distributor(),
                        'products' => [],
                    ];
                }
                array_push($cartData_shop[$singleData['distributor_id']]['products'], $singleData);
            }
            $this->data['cartData_shop'] = $cartData_shop;

            if (count($cartData_shop) == 0) {
                $this->error(Lang::get('購物車內無商品'));
            }

            return view('home.cart.choose_shop', ['data' => $this->data]);
        } else {
            /*自動把所有商品拋轉至cart*/
            session()->put('cart', $Proposal->projectData['data']);
            $this->redirect(url('Cart/cart'));
        }
    }

    public function do_choose_shop()
    {
        session()->forget('cart');

        $select_prods = request()->post('select_prods') ?? [];
        if (count($select_prods) == 0) {
            $this->error(Lang::get('請選擇要結帳之商品'));
        }

        /* 讀取購物車商品 */
        $Proposal = OrderHelper::get_Proposal_GetCartData('cart_all', $this->user["id"]);
        $cart_all = $Proposal->getCartArray();

        /*拋轉商品至cart*/
        $cartData = [];
        foreach ($select_prods as $key => $value) {
            if (!isset($cart_all[$key])) {
                continue;
            }
            // $singleData = Proposal::get_singleData($key, 'online'); /* 取得商品資料 */

            // /*檢查供應商是否同一個*/
            // if(!isset($distributor_id)){
            //     $distributor_id = $singleData['distributor_id'];
            // }else if($distributor_id != $singleData['distributor_id']){
            //     $this->error(Lang::get('選擇的商品包含不同賣家，無法同時結帳'));
            // }

            /*記錄拋轉資料*/
            $cartData[$key] = $cart_all[$key];
        }

        if ($cartData) {
            session()->put('cart', json_encode($cartData));
            $this->redirect(url('Cart/cart'));
        } else {
            $this->error(Lang::get('購物車內無商品'));
        }
    }

    public function cart()
    {

        // 有點過別人的商品推廣連結
        $productinfo_recommend = session()->get('productinfo_recommend');
        $this->data['productinfo_recommend'] = $productinfo_recommend;

        // 控制前台是否顯示選擇優惠的內容
        $marketing = 0;
        // $marketingItems = ['活動優惠', '折扣優惠', '優惠券專區', '點數設定', '加價購設定', '直接輸入型優惠券', '消費累積兌換', '消費抽抽樂'];
        $marketingItems = ['會員優惠設定', '點數設定', '直接輸入型優惠券', '優惠券專區', '活動優惠'];

        foreach ($marketingItems as $key => $value) {
            if (empty(config('control.close_function_current')[$value])) {
                $marketing += 1;
            }
        }

        $this->data['marketing'] = $marketing;

        $place_select_result = request()->post();
        if (isset($place_select_result['ExtraData'])) {
            $temp_order_data = DB::table('temp_order_data')->where([
                'randomkey' => $place_select_result['ExtraData'],
                'over' => 0
            ])->first();
            $temp_order_data = CommonService::objectToArray($temp_order_data);

            if (!$temp_order_data) {
                $this->error(Lang::get('資料不完整'), url('Cart/cart'));
            }

            $order_data = json_decode($temp_order_data['order_data'], true);
            session()->put('_token', $order_data['_token']);
            session()->put('cart', $temp_order_data['cart_data']);

            $MemberInstance = new MemberInstance($temp_order_data['user_id']);
            $user = $MemberInstance->get_user_data();
            session()->put('user', $user);
            $this->user = $user;
            /*調整紀錄的登入資訊(如同 app\Http\Controllers\home\PublicController.php 的 __construct() 操作)*/
            $this->user["id"] = $this->user["id"] ?? 0;

            if ($this->user["id"] == 0) {
                $this->user = [
                    'id'        => '0',
                    'name'        => '',
                    'email'        => '',
                    'phone'        => '',
                    'tele'        => '',
                    'permission' => '',
                    'user_type' => '0',
                    'product_view_id' => '1',
                ];
            }

            $this->data['user'] = $this->user;
            $OrderData = $order_data;
            $OrderData['selectPlace'] = $place_select_result;
            DB::table('temp_order_data')->whereRaw('randomkey="' . $place_select_result['ExtraData'] . '"')->update(['over' => 1]);
        } else {
            $OrderData['selectPlace'] = [];
        }

        $Proposal = OrderHelper::get_Proposal_GetCartData('cart_all', $this->user["id"]);
        $cart = $Proposal->getCartArray();

        $this->data['OrderData'] = json_encode($OrderData, JSON_UNESCAPED_UNICODE);
        $this->data['ATM_ExpireDate'] = config('ecpay.ATM_ExpireDate');
        $get_fields_type_par = ExaminationHelper::get_fields_type_par();
        foreach ($get_fields_type_par as $key => $value) {
            $this->data[$key] = $value;
        }

        $city = DB::table('city')->get();
        $this->data['city'] = CommonService::objectToArray($city);

        /*取得可加價購商品*/
        $addprice_group = Proposal::get_addprice_products_by_cart();
        $this->data['addprice_group'] = $addprice_group;

        $MemberInstance = new MemberInstance($this->user['id']);
        $user_data = $MemberInstance->get_user_data();
        $home = $user_data ? $user_data['home'] : '';
        $this->data['home'] = $home;
        $invoice = $user_data ? $user_data['invoice'] : '';
        $this->data['invoice'] = $invoice;

        //各種同意書
        $consent = DB::table('consent')->find("1");
        $this->data['consent'] = CommonService::objectToArray($consent);

        // 取得使用者的email(若使用者一開始沒有輸入，而是在為登出的前一次輸入並存入的話，本次仍不會自動帶入，故需協助帶入。)
        if (empty($this->data['user']['email']) || $this->data['user']['email'] == '' || $this->data['user']['email'] == null) {
            $MemberInstance = new MemberInstance($this->user['id']);
            $user_data = $MemberInstance->get_user_data();
            $this->data['user']['email'] = $user_data['email'];
        }

        return view('home.cart.cart', ['data' => $this->data]);
    }

    public function ajax_cart_data()
    {
        $cart_session = request()->post('cart_session') ?? 'cart';
        $Proposal = OrderHelper::get_Proposal_GetCartData($cart_session, $this->user["id"]);
        $cartData = [];

        foreach ($Proposal->getCartArray() as $key => $value) {
            $singleData = Proposal::get_singleData($key, 'online'); /* 取得商品資料 */
            $singleData['num'] = $value;

            array_push($cartData, $singleData);
        }
        $return_data['cartData'] = $cartData;

        /*報名相關資料*/
        $get_fields_type_par = ExaminationHelper::get_fields_type_par();
        foreach ($get_fields_type_par as $key => $value) {
            $return_data[$key] = $value;
        }

        /* 處理運費 start */
        $shipping = OrderHelper::get_shipping_method($cartData);
        $return_data['shipping'] = array_values($shipping);
        /* 處理運費 end */

        /* 處理付款方法 start */
        $paying = $this->get_paying_method($cartData);
        $return_data['paying'] = array_values($paying);
        /* 處理付款方法 end */

        /* 處理捐贈碼 start */
        $love_code_list = Db::table('lovecode')->whereRaw('online=1')->get();
        $return_data['love_code_list'] = CommonService::objectToArray($love_code_list);
        /* 處理捐贈碼 end */

        /* 處理折扣 start */
        $discount = $this->getdiscount();
        $return_data['discount'] = [$discount];
        /* 處理折扣 end */

        $return_data['exchange_rate'] = config('extra.skychakra.exchange_rate');

        return $return_data;
    }

    public function getdiscount()
    {
        $Proposal = OrderHelper::get_Proposal_GetCartData('cart', $this->user["id"]);
        $cart = $Proposal->getCartArray();

        $points_limit = 0; // 限用點數
        $need_shipfee = false; /* 預設不須處理運費 */
        $cartData = [];
        foreach ($cart as $key => $value) {
            $singleData = Proposal::get_singleData($key, 'online'); /* 取得商品資料 */

            /* 有購買 一般商品 或 網紅商品 或 加價購商品 */
            if (
                $singleData['key_type'] == 'normal'
                || substr($singleData['key_type'], 0, 3) == 'kol'
                || $singleData['key_type'] == 'add'
                || substr($singleData['key_type'], 0, 8) == 'askprice'
            ) {
                $need_shipfee = true; /* 需處理運費 */

                /*讀取對應限用點數的分館的設定*/
                if (!isset($points_setting)) {
                    if (config('control.control_platform') == 1) {
                        $distributor_id = $singleData['distributor_id'];
                    } else {
                        $distributor_id = 0;
                    }
                    $points_setting = json_decode(DB::table('points_allow_use')->where('distributor_id', $distributor_id)->first()->value ?? '[]');
                }
                if ($points_setting) {
                    /* 檢查商品是否適用點數 */
                    $final_array = DB::table('productinfo')->select('final_array')->find($singleData['type_product_id'])->final_array;
                    foreach ($points_setting as $prev_id) {
                        if (strpos($final_array, '"prev_id":"' . $prev_id . '"') !== false) { // 此館可以使用點數
                            $points_limit += ($singleData['countPrice'] * $value); // 累計可使用的點數量
                            break; // 換下個商品
                        };
                    }
                } else { /*皆未設定視為允許全部*/
                    $points_limit += ($singleData['countPrice'] * $value); // 累計可使用的點數量
                }
            }

            $singleData['num'] = $value;
            array_push($cartData, $singleData);
        }
        // dump($cartData);exit;
        $DiscountProposal = DiscountProposal::withTeamMembersAndRequire(
            ['Total', 'CouponCheck', 'PointCheck', 'ActCalculate', 'MemberDiscount', 'ContributionDeduct'],
            [
                'user_id' => $this->user["id"] ? $this->user["id"] : '0',
                'cartData' => $cartData
            ]
        );
        $DiscountProposal = DiscountMemberFactory::createNextMember($DiscountProposal);

        if ($DiscountProposal->projectData["points"]) {
            $points_limit = $points_limit > $DiscountProposal->projectData["points"][0]["point"] ? $DiscountProposal->projectData["points"][0]["point"] : $points_limit;
        }
        $this->data['points_limit'] = $points_limit;
        $this->data['discountData'] = $DiscountProposal->projectData;
        $this->data['coupon_c'] = count($DiscountProposal->projectData['coupon']);
        $this->data['acts_c'] = count($DiscountProposal->projectData['acts']['actCart']);

        return [
            'points_limit' => $points_limit,
            'discountData' => $DiscountProposal->projectData,
            'coupon_c' => count($DiscountProposal->projectData['coupon']),
            'acts_c' => count($DiscountProposal->projectData['acts']['actCart']),
        ];
    }

    public function selectPlace()
    {
        if (empty(session()->get('cart')) == true) {
            $this->error(Lang::get('購物車內無商品'));
        }

        $Order_Data = request()->get('Order_Data');
        session()->put('Order_Data', $Order_Data);

        $user_id = $this->user["id"] ? $this->user["id"] : 0;
        $temp_order_data = [
            'user_id'   => $user_id,
            'order_data' => $Order_Data,
            'cart_data' => session()->get('cart'),
            'randomkey' => time() . CommonService::randomkeys(10),
            'time'      => date("Y-m-d H:i:s"),
        ];
        $temp_order_data_id = DB::table('temp_order_data')->insertGetId($temp_order_data);

        $Order_Data = json_decode($Order_Data);

        // 電子地圖
        define('HOME_URL', 'http://www.sample.com.tw/logistics_dev');
        include(ROOT_PATH . 'app/Services/ThirdParty/Ecpay.Logistic.Integration.php');

        try {
            $AL = new \EcpayLogistics();

            //檢查是否需超商付款
            $IsCollection = $Order_Data->pay_way == Lang::get('貨到付款') ? \EcpayIsCollection::YES : \EcpayIsCollection::NO;

            //檢查是否需超商付款
            $Device = parent::isMobileCheck() ? \EcpayDevice::MOBILE : \EcpayDevice::PC;

            //檢查哪種超商取貨
            $send_way = DB::table('shipping_fee')->whereRaw('id=' . $Order_Data->send_way)->first();
            $send_way = CommonService::objectToArray($send_way);
            if (!$send_way) $this->error(Lang::get('無此運送方式'));

            if ($send_way['name'] == Lang::get('全家取貨')) {
                $LogisticsSubType = \EcpayLogisticsSubType::FAMILY; //FAMILY(B2C), FAMILY_C2C(C2C)
            } elseif ($send_way['name'] == Lang::get('7-11取貨')) {
                $LogisticsSubType = \EcpayLogisticsSubType::UNIMART; //UNIMART(B2C), UNIMART_C2C(C2C)
            } elseif ($send_way['name'] == Lang::get('萊爾富取貨')) {
                $LogisticsSubType = \EcpayLogisticsSubType::HILIFE; //HILIFE(B2C), HILIFE_C2C(C2C)
            } else {
                $this->error(Lang::get('無此運送方式'));
            }

            $AL->Send = array(
                'MerchantID' => config('extra.ecpay.MerchantID'),
                'MerchantTradeNo' => 'no' . date('YmdHis'),
                'LogisticsSubType' => $LogisticsSubType, //FAMI、UNIMART、HILIFE
                'IsCollection' => $IsCollection,
                'ServerReplyURL' => url('Cart/cart'),
                'ExtraData' => $temp_order_data['randomkey'],
                'Device' => $Device
            );
            // dd($AL->Send);
            // CvsMap(Button名稱, Form target)
            $html = $AL->CvsMap(Lang::get('電子地圖'));
            $html .= '<script>document.getElementById("__paymentButton").click();</script>';
            echo $html;
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }

    /**
     * 確定訂單
     */
    public function buy()
    {
        $OrderData = request()->post();

        $Proposal = OrderHelper::get_Proposal_GetCartData('cart', $this->user["id"]);
        $cart = $Proposal->getCartArray();

        $distributor_id = 0;
        $points_limit = 0;
        $cartData = [];
        foreach ($cart as $cart_key => $v) {
            $singleData = Proposal::get_singleData($cart_key, 'online'); /* 取得商品資料 */
            $singleData['num'] = $v;
            array_push($cartData, $singleData);

            $distributor_id = $singleData['distributor_id'];

            // 依商品類型檢查庫存
            switch ($singleData['key_type']) {
                case 'normal': // 一般商品 或 某網紅的商品 或 加價購商品
                case substr($singleData['key_type'], 0, 3) == 'kol':
                case 'add':
                case substr($singleData['key_type'], 0, 8) == 'askprice':
                    // 找好所有購買商品資料
                    $prod_data = DB::table('productinfo_type as pt')
                        ->join('productinfo as p', 'pt.product_id', '=', 'p.id')
                        ->select(
                            'pt.num',
                            'pt.title',
                            'pt.count as pt_count',
                            'p.title as pro_name',
                            'p.card_pay',
                            'p.is_registrable',
                            'p.final_array'
                        )->where('pt.id', $singleData['type_id_ori'])->first();
                    $prod_data = CommonService::objectToArray($prod_data);
                    // 檢查商品是否可刷卡
                    if (config('control.control_card_pay') == 1 && in_array($OrderData['pay_way'], [3, 4]) && $prod_data['card_pay'] == 0) {
                        $this->error(Lang::get('有不可刷卡之商品') . '：' . $prod_data['pro_name']);
                    }

                    // 檢查報名
                    if (config('control.control_register') == 1 && $prod_data['is_registrable'] == 1) {
                        /* 檢查報名資料數量是否正確 (依品項、不管報名者id、報名未成立)*/
                        $examinees_count = count(ExaminationHelper::get_myself_type_examinee_info($cart_key, $examinee_id = 0, $order_id = 0, "", ['backstage' => false]));

                        if ($v != $examinees_count) {
                            $this->error(Lang::get('報名者數量與填寫資料不符') . '：' . $prod_data['pro_name']);
                        }
                    }

                    /*讀取對應限用點數的分館的設定*/
                    if (!isset($points_setting)) {
                        $points_setting = json_decode(DB::table('points_allow_use')->where('distributor_id', $distributor_id)->first()->value ?? '[]');
                    }
                    if ($points_setting) {
                        /* 檢查商品是否適用點數 */
                        foreach ($points_setting as $prev_id) {
                            if (strpos($prod_data['final_array'], '"prev_id":"' . $prev_id . '"') !== false) { // 此館可以使用點數
                                $points_limit += ($prod_data['pt_count'] * $v); // 累計可使用的點數量
                                break; // 換下個商品
                            };
                        }
                    } else { /*皆未設定視為允許全部*/
                        $points_limit += ($prod_data['pt_count'] * $v); // 累計可使用的點數量
                    }
                    break;

                case 'coupon': // 優惠券商品
                    break;

                default:
                    break;
            }

            if (empty(config('control.close_function_current')['庫存警示'])) {
                // 依購物車減數量
                $OrderData['cart_session'] = 'cart';
                $check_result = OrderHelper::cartCtrl($cart_key, 0, 'increase', $OrderData['cart_session'], session('user.id'));

                if ($check_result['code'] == 0) {
                    $this->error(Lang::get('請修改購買數量') . "<br>" . $check_result['msg']);
                }
            }
        }

        // 檢查付款方式
        $paying = $this->get_paying_method($cartData, $OrderData['pay_way']);
        if (count($paying) == 0) {
            $this->error(Lang::get('無法使用此付款方式'));
        }

        if (strpos($OrderData['discount'], 'points') !== false) {
            if ($OrderData['point'] > $points_limit) {
                $this->error(Lang::get('超出可使用點數上限'));
            }
        }

        // 如果訂單輸入的手機號碼與訂購帳戶相同(即本人購買)->檢查用戶資料是否缺失->若缺失直接以此訂單補回
        if ($OrderData['transport_location_phone'] == $this->user['phone']) {
            // 根據輸入的手機號碼從 main_db 查詢用戶資料
            $user = DB::connection('main_db')->table('account')
                ->where('phone', $OrderData['transport_location_phone'])
                ->first();

            // 非超商取貨，將地址寫入
            $OrderData['addrC'] = ($OrderData['send_way'] == '8') ? '' : $OrderData['addrC'];

            if ($user) {
                $user_data = CommonService::objectToArray($user);

                // 檢查並批量更新缺失的用戶資料
                $updateData = [];

                if (empty($user_data['email'])) {
                    $updateData['email'] = $OrderData['transport_email'];
                }

                if (empty($user_data['name'])) {
                    $updateData['name'] = $OrderData['transport_location_name'];
                }

                if (empty($user_data['home'])) {
                    $updateData['home'] = $OrderData['addrC'];
                }

                if (empty($user_data['tele'])) {
                    $updateData['tele'] = $OrderData['transport_location_phone'];
                }

                if (!empty($updateData)) {
                    DB::connection('main_db')->table('account')
                        ->where('id', $user_data['id'])
                        ->update($updateData);
                }
            }
        }

        // 多供應商支援：設定 distributor_id = 0，由 orderform_distributors 關聯表處理
        // 原本的單一供應商邏輯已改為多供應商支援
        $OrderData['distributor_id'] = 0;

        try {
            $OrderData = OrderHelper::createOrder($OrderData, 'online', $this->user["id"] ?? 0);
            if ($OrderData['become_member']) {
                $this->user = $OrderData['become_member'];
            }
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }


        /*將填寫的考生資料配合上訂單id(報名成立)*/
        $this->turn_exinfo_complete($OrderData);

        return $this->redirect2Next($OrderData);
    }

    public function redirect3Next()
    {
        $once = DB::connection('main_db')->table('orderform')->where("id", request()->post('id'))->first();
        $once = CommonService::objectToArray($once);
        if ($this->user["id"] == 0 && $once['user_id'] != 0) { // 訂單為會員訂單，且未登入會員
            $this->error(Lang::get('請先登入會員'));
        }

        //create order number
        $order_number = OrderHelper::create_order_number();
        //----
        $globalMailData = parent::getMailData();
        $order_number_change_letter = Lang::get('menu.訂單編號變更信消費者');
        $order_number_change_letter = str_replace("{globalMailData_mailFromName}", $globalMailData['mailFromName'], $order_number_change_letter);
        $order_number_change_letter = str_replace("{order_number_change}", $once['order_number'] . " => " . $order_number, $order_number_change_letter);
        $mailBody = "
            <html>
                <head></head>
                <body>
                    <div>
                        " . $order_number_change_letter . "
                    </div>
                    <div>
                    " . $globalMailData['system_email']['order_complete'] . "
                    </div>
                    <div style='color:red;'>
                        ≡ " . Lang::get('此信件為系統自動發送，請勿直接回覆') . "(" . $once['id'] . ") ≡
                    </div>
                </body>
            </html>
        ";
        // dump($mailBody);exit;
        $mail_return = parent::Mail_Send($mailBody, 'client', $once['transport_email'], Lang::get('訂單編號變更'));

        DB::connection('main_db')->table('orderform')->where('id', $once['id'])->update(['show_date' => '1', 'order_number' => $order_number]);
        $data = [
            'id' => $once['id'],
            'total' => $once['total'],
            'order_number' => $order_number,
            'pay_way' => $once['payment'],
            'email' => $once['transport_email'],
        ];
        $this->redirect2Next($data);
    }

    /**
     * 判斷金流方式
     */
    private function redirect2Next($OrderData)
    {
        if ($OrderData['total'] > 0) {
            if (config('control.thirdpart_money') == 1) {
                if (in_array($OrderData['pay_way'], ['3', '4'])) { /*線上刷卡、分期付款*/
                    return $this->ecpy_card_pay($OrderData); /*綠界刷卡*/
                    // return $this->tspg_card_pay($OrderData);   /*台新刷卡*/

                } else if ($OrderData['pay_way'] == '5') { /*LINE Pay*/
                    return $this->line_pay($OrderData);
                }
            }
        }

        $this->redirect(url('Orderform/orderform_success') . '?id=' . $OrderData['order_number']);
    }

    /**
     * 綠界刷卡
     */
    private function ecpy_card_pay($OrderData)
    {
        /* 綠界 */
        include(ROOT_PATH . 'app/Services/ThirdParty/ECPay.Payment.Integration.php');

        try {
            $obj = new \ECPay_AllInOne();

            //服務參數
            $obj->ServiceURL  = config('extra.ecpay.ServiceURL');
            $obj->HashKey     = config('extra.ecpay.HashKey');
            $obj->HashIV      = config('extra.ecpay.HashIV');
            $obj->MerchantID  = config('extra.ecpay.MerchantID');
            $obj->EncryptType = '1';


            $globalMailData = parent::getMailData();
            //基本參數(請依系統規劃自行調整)

            $MerchantTradeNo = $OrderData['order_number'];
            $obj->Send['ReturnURL'] = url('Ecreturn/returnurl') . '/' . $OrderData['id'];

            $obj->Send['MerchantTradeNo']   = $MerchantTradeNo;                 //訂單編號
            $obj->Send['MerchantTradeDate'] = date('Y/m/d H:i:s');              //交易時間
            $obj->Send['TotalAmount']       = (int)$OrderData['total'];         //交易金額
            $obj->Send['TradeDesc']         = $globalMailData['mailFromName'];  //交易描述
            $obj->Send['ChoosePayment']     = \ECPay_PaymentMethod::Credit;    //付款方式:Credit
            $backurl = url('Orderform/orderform_success') . "?id=" . $OrderData['order_number'];
            $obj->Send['ClientBackURL']     = $backurl;                         //付款完成通知回傳的網址
            $obj->Send['NeedExtraPaidInfo'] = 'Y';
            $obj->Send['Remark']            = $OrderData['id'];                 //備註
            //var_dump($obj->Send['ReturnURL']);die();

            //訂單的商品資料
            array_push($obj->Send['Items'], [
                'Name' => $globalMailData['mailFromName'],
                'Price' => (int)$OrderData['total'],
                'Currency' => "元",
                'Quantity' => (int) "1",
                'URL' => "dedwed"
            ]);
        } catch (\Exception $e) {
            echo $e->getMessage();
        }

        if ($OrderData['pay_way'] == 3) { /*線上刷卡*/
            $obj->CheckOut();
        } else if ($OrderData['pay_way'] == 4) { /*分期付款*/
            $obj->SendExtend['CreditInstallment'] = config('extra.ecpay.CreditInstallment');    //分期期數，預設0(不分期)，信用卡分期可用參數為:3,6,12,18,24
            $obj->SendExtend['InstallmentAmount'] = (int)$OrderData['total'];                  //使用刷卡分期的付款金額，預設0(不分期)
            $obj->SendExtend['Redeem'] = false;                                                //是否使用紅利折抵，預設false
            $obj->SendExtend['UnionPay'] = false;                                               //是否為聯營卡，預設false;
            $obj->CheckOut();
        }
    }

    private function tspg_card_pay($OrderData)
    {
        if ($OrderData['pay_way'] == 3) { /*線上刷卡*/
            $backurl = url('Orderform/orderform_success') . "?id=" . $OrderData['order_number'];

            $globalMailData = parent::getMailData();

            $Tspg = new Tspg();
            $res = $Tspg->auth($OrderData['order_number'], $OrderData['total'], $globalMailData['mailFromName'], $backurl, $OrderData['id']);
            // dump($res);exit;
            if ($res->params->ret_code == '00') {
                $this->redirect($res->params->hpp_url);
                // redirect($res->params->post_back_url);
            } else {
                $this->error(Lang::get('發生錯誤'), url('Orderform/orderform_success') . '?id=' . $OrderData['order_number']);
            }
        }
    }
    private function line_pay($OrderData)
    {
        $globalMailData = parent::getMailData();

        $LinePay = new LinePay();
        $res = $LinePay->auth($OrderData, $globalMailData['mailFromName']);

        if ($res->returnCode == '0000') {
            if ($LinePay->isMobile() == true) {
                $this->redirect($res->info->paymentUrl->app);
            } else {
                $this->redirect($res->info->paymentUrl->web);
            }
        } else {
            $this->error(Lang::get('發生錯誤'), url('Orderform/orderform_success') . '?id=' . $OrderData['order_number']);
        }
    }

    /*AJAX*/
    public function cartCtrl(Request $request)
    {
        $product_id = $request->post('product_id');
        $num = $request->post('num');
        $cmd = $request->post('cmd') ?? '';
        $cart_session = $request->post('cart_session') ?? '';
        return OrderHelper::cartCtrl($product_id, $num, $cmd, $cart_session, $this->user["id"]);
    }


    /**
     * 將填寫的考生資料配合上訂單id(報名成立)
     * 這是三小功能@@...
     */
    private function turn_exinfo_complete($OrderData)
    {
        $product = $OrderData['product'];
        foreach ($product as $key => $value) {
            if (isset($value['key_type'])) {
                if (in_array($value['key_type'], Proposal::NOT_PRODUCTINFO_PRODTYPE)) { /*不屬於 productinfo商品*/
                    continue; /*略過處理報名資料*/
                }

                /* 檢查報名資料數量是否正確 (依品項、不管報名者id、報名未成立)*/
                $examinees = ExaminationHelper::get_myself_type_examinee_info($value["type_id"], $examinee_id = 0, $order_id = 0);
                foreach ($examinees as $examinee) {
                    ExaminationHelper::send_complete_order_email($examinee['id']);
                    ExaminationHelper::send_special_email($examinee['id']);
                    $data = ['order_id' => $OrderData['id'], 'datetime' => time(), 'reg_status' => 1];
                    if (isset($value['pre_buy'])) { /*如果是預購，修改reg_status為候補*/
                        if ($value['pre_buy']) {
                            $data['reg_status'] = 0;
                        }
                    }
                    DB::table('examinee_info')->find($examinee['id'])->update($data);
                }
            }
        }
    }

    public function get_paying_method($cartData, $paying_id = false)
    {
        $can_card_pay = 0; // 預設不可刷卡
        if (!$cartData) {
            return [];
        }

        // dump($cartData);
        //--- 取交集: 可付款方法為所有限制付款方法的交集
        if (config('control.control_product_paying') == 1) { /* 有啟用商品關聯付款方法 */
            foreach ($cartData as $key => $value) {
                if ($value['pay_type'] == "") { // 無勾選付款方法
                    if (!isset($paying_type)) { // 還未設定過可運付款方法
                        $paying_type = [];
                        $paying_all = DB::table('pay_fee')->where('sys_status', 1)->orderByRaw('order_id asc, id desc')->get();
                        $paying_all = CommonService::objectToArray($paying_all);
                        array_walk($paying_all, function ($item) use (&$paying_type) {
                            array_push($paying_type, $item['id']);
                        });
                    }
                } else { // 有勾選付款方法
                    if (!isset($paying_type)) { // 還未設定過可運付款方法
                        $paying_type = explode(',', $value['pay_type']);
                    } else {
                        $paying_type = array_intersect($paying_type, explode(',', $value['pay_type']));
                    }
                }
            }
            $paying_where = isset($paying_type) ? array_filter($paying_type, function ($v) {
                return $v != null;
            }) : [];
            $paying_where = $paying_where ? "id in (" . join(',', $paying_where) . ")" : 'id=-1';
        } else {
            $paying_where = '1=1';
        }

        /* 處理刷卡 start */
        if (config('control.thirdpart_money') == 1) { /*有開放第三方金流*/
            $can_card_pay = 1; // 預設可以刷卡
            if (config('control.control_card_pay') == 1) { /*可設定商品是否能刷卡*/
                array_walk($cartData, function ($item) use (&$can_card_pay) { /*依購物車商品檢查可否刷卡*/
                    if ($item['card_pay'] == 0) {
                        $can_card_pay = 0;
                    }
                });
            }
        }
        if ($can_card_pay != 1) { /*檢查後不開放使用刷卡功能*/
            $paying_where .= ' AND id not in (3,4)';
        }
        // dump($paying_where);
        // dump($can_card_pay);
        /* 處理刷卡 end */

        if ($paying_id) {
            $paying_where .= ' AND id ="' . $paying_id . '"';
        }
        // dump($paying_where);exit;
        $paying_fee = DB::table('pay_fee')->whereRaw($paying_where)->whereRaw('sys_status=1')->orderByRaw('order_id asc, id desc')->get();
        $paying_fee = CommonService::objectToArray($paying_fee);
        foreach ($paying_fee as $key => $value) {
            // $value['name'] = str_replace('\\', '\\\\', $value['name']);

            /*合併供應商設定資料*/
            if (config('control.control_platform') == 1) {
                $distributor_id = $cartData[0]['distributor_id'];
            } else {
                $distributor_id = 0;
            }
            $admin_type = $distributor_id == 0 ? 'admin' : 'distribution';
            $paying_fee[$key] = CartMethod::merge_distributor_setting($value, 'pay_fee', $distributor_id);
            if (!isset($paying_fee[$key]['pay_fee_id'])) {
                $paying_fee[$key]['pay_fee_id'] = $value['id'];
            }
        }
        $paying_fee = array_filter($paying_fee, function ($v) {
            return $v['online'] == 1;
        });
        // dump($paying_fee);exit;
        return $paying_fee;
    }

    public function check_Invoice_Infos($insertData)
    {
        if ($insertData['Donation'] == '1') { /*捐贈*/
            $insertData['uniform_numbers'] = ''; /*捐贈不可帶統編*/
            $insertData['company_title'] = ''; /*捐贈不帶公司抬頭*/
            $insertData['Print'] = 0; /*捐贈不可列印*/
            if ($insertData['LoveCode'] == '') {
                $this->error('未設定捐贈對象');
            } else { /*驗證捐贈對象*/
                $r = Invoice::instance()->check_love_code($insertData['LoveCode']);
                if ($r['RtnCode'] != 1) {
                    $this->error($r['RtnMsg']);
                }
            }
        } else { /*自領*/
            $insertData['LoveCode'] = '';
            if ($insertData['CarrierType'] != '') { /*使用載具*/
                $insertData['Print'] = 0; /*載具不可列印*/
            } else {
                if (in_array($insertData['InvoiceType'], [1, 2]) == true) {
                    $insertData['Print'] = 1; /*要列印*/
                } else {
                    $insertData['Print'] = 0; /*不列印*/
                }

                if ($insertData['transport_location'] == '') {
                    $insertData['Print'] = 0; /*無地址不可列印*/
                }
            }
            if ($insertData['uniform_numbers'] != '') { /*要統一編號*/
                if ($insertData['company_title'] == '') {
                    $this->error('請輸入公司抬頭');
                }
            }
        }
        if ($insertData['CarrierType'] != '') { /*使用載具*/
            if ($insertData['CarrierType'] == '1') { /*綠界電子發票載具*/
                $insertData['CarrierNum'] = '';
            } else {
                $r = Invoice::instance()->check_barcode($insertData['CarrierNum']);
                if ($r['RtnCode'] != 1) {
                    $this->error($r['RtnMsg']);
                }
            }
        } else {
            $insertData['CarrierNum'] = '';
        }

        return $insertData;
    }

    public function invoice_lovecode_check()
    {
        $LoveCode = request()->get('LoveCode');
        $result = Invoice::instance()->check_love_code($LoveCode);

        if ($result['RtnCode'] == 1) {
            return ['code' => 1, 'msg' => 'OK'];
        } else {
            return ['code' => 0, 'msg' => '捐贈碼檢查有誤，請重新輸入'];
        }
    }

    public function invoice_carrier_check()
    {
        $CarrierNum = request()->get('CarrierNum');
        $result = Invoice::instance()->check_barcode($CarrierNum);

        if ($result['RtnCode'] == 1) {
            return ['code' => 1, 'msg' => 'OK'];
        } else {
            return ['code' => 0, 'msg' => '載具編號檢查有誤，請重新輸入'];
        }
    }
}
