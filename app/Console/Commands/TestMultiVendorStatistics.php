<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Repositories\Main\OrderformDistributorRepository;
use App\Http\Controllers\order\OrderCtrl;
use App\Http\Controllers\order\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TestMultiVendorStatistics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:multi-vendor-statistics';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '測試多供應商統計功能';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('開始測試多供應商統計功能...');

        // 1. 測試 OrderformDistributorRepository 統計功能
        $this->testRepositoryStatistics();

        // 2. 測試訂單分組統計
        $this->testOrderGroupStatistics();

        // 3. 測試供應商回饋統計
        $this->testSupplierBonusStatistics();

        $this->info('多供應商統計功能測試完成！');
        return 0;
    }

    /**
     * 測試 Repository 統計功能
     */
    private function testRepositoryStatistics()
    {
        $this->info('=== 測試 Repository 統計功能 ===');
        
        $repo = new OrderformDistributorRepository();
        $stats = $repo->getStatistics();
        
        $this->info("總關聯數: {$stats['total_relations']}");
        $this->info("平台訂單數: {$stats['platform_orders']}");
        $this->info("供應商訂單數: {$stats['vendor_orders']}");
        $this->info("多供應商訂單數: {$stats['multi_vendor_orders']}");
        $this->info("供應商數量: {$stats['distributor_count']}");
        
        // 測試特定供應商統計
        if ($stats['distributor_count'] > 0) {
            $distributorStats = $repo->getDistributorStatistics(4); // 測試供應商 4
            $this->info("供應商 4 的訂單數: {$distributorStats['order_count']}");
            $this->info("供應商 4 參與的多供應商訂單數: {$distributorStats['multi_vendor_count']}");
        }
    }

    /**
     * 測試訂單分組統計
     */
    private function testOrderGroupStatistics()
    {
        $this->info('=== 測試訂單分組統計 ===');
        
        // 模擬 OrderCtrl 的 group_excel 邏輯
        $distributors = [];
        
        // 獲取一些測試訂單
        $orders = DB::connection('main_db')
            ->table('orderform')
            ->limit(10)
            ->get();
        
        foreach ($orders as $order) {
            $orderform_id = $order->id;
            
            // 獲取該訂單的所有供應商關聯
            $orderDistributors = DB::connection('main_db')
                ->table('orderform_distributors')
                ->where('orderform_id', $orderform_id)
                ->get();
            
            if ($orderDistributors->isEmpty()) {
                $distributorIds = [$order->distributor_id];
            } else {
                $distributorIds = $orderDistributors->pluck('distributor_id')->toArray();
            }
            
            // 計算每個供應商應分配的金額
            $distributorCount = count($distributorIds);
            $amountPerDistributor = $distributorCount > 0 ? $order->total / $distributorCount : 0;
            
            foreach ($distributorIds as $distributorId) {
                $key = 'k_' . $distributorId;
                if (!isset($distributors[$key])) {
                    $distributors[$key] = [
                        'distributor_id' => $distributorId,
                        'total' => 0,
                        'order_count' => 0
                    ];
                }
                $distributors[$key]['total'] += $amountPerDistributor;
                $distributors[$key]['order_count']++;
            }
        }
        
        $this->info('訂單分組統計結果:');
        foreach ($distributors as $key => $data) {
            $this->info("供應商 {$data['distributor_id']}: 總金額 {$data['total']}, 訂單數 {$data['order_count']}");
        }
    }

    /**
     * 測試供應商回饋統計
     */
    private function testSupplierBonusStatistics()
    {
        $this->info('=== 測試供應商回饋統計 ===');
        
        // 獲取一些供應商回饋資料
        $bonusData = DB::connection('main_db')
            ->table('orderform_product')
            ->where('distributor_id', '>', 0)
            ->where('price_supplier', '>', 0)
            ->limit(10)
            ->get();
        
        $data_group = [];
        foreach ($bonusData as $value) {
            if (!isset($data_group[$value->distributor_id])) {
                $data_group[$value->distributor_id] = [
                    'distributor_id' => $value->distributor_id,
                    'total_bonus' => 0,
                    'product_count' => 0
                ];
            }
            $data_group[$value->distributor_id]['total_bonus'] += $value->price_supplier;
            $data_group[$value->distributor_id]['product_count']++;
        }
        
        $this->info('供應商回饋統計結果:');
        foreach ($data_group as $data) {
            $this->info("供應商 {$data['distributor_id']}: 總回饋 {$data['total_bonus']}, 商品數 {$data['product_count']}");
        }
    }
}
