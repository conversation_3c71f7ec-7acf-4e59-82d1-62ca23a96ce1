<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateOrderformDistributors extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:orderform-distributors
                            {--dry-run : 僅顯示將要遷移的資料，不實際執行}
                            {--batch-size=1000 : 批量處理大小}
                            {--rollback : 回滾遷移}
                            {--verify : 驗證遷移結果}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '遷移歷史訂單資料到 orderform_distributors 關聯表';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if ($this->option('rollback')) {
            return $this->rollback();
        }

        if ($this->option('verify')) {
            return $this->verify();
        }

        return $this->migrate();
    }

    /**
     * 執行資料遷移
     */
    private function migrate()
    {
        $batchSize = (int) $this->option('batch-size');
        $isDryRun = $this->option('dry-run');

        $this->info('開始遷移訂單供應商關聯資料...');

        if ($isDryRun) {
            $this->warn('*** DRY RUN 模式 - 不會實際執行資料變更 ***');
        }

        // 檢查關聯表是否為空
        $existingCount = DB::connection('main_db')->table('orderform_distributors')->count();
        if ($existingCount > 0 && !$isDryRun) {
            if (!$this->confirm("關聯表中已有 {$existingCount} 筆資料，是否要清空後重新遷移？")) {
                $this->info('遷移已取消');
                return Command::FAILURE;
            }
            DB::connection('main_db')->table('orderform_distributors')->truncate();
            $this->info('已清空關聯表');
        }

        // 收集所有需要遷移的關聯資料
        $relations = $this->collectRelations();

        if (empty($relations)) {
            $this->info('沒有需要遷移的資料');
            return Command::SUCCESS;
        }

        $this->info("總共收集到 " . count($relations) . " 筆關聯資料");

        if ($isDryRun) {
            $this->displayDryRunResults($relations);
            return Command::SUCCESS;
        }

        // 批量插入資料
        $this->insertRelations($relations, $batchSize);

        $this->info('遷移完成！');
        return Command::SUCCESS;
    }

    /**
     * 收集所有需要遷移的關聯資料
     */
    private function collectRelations()
    {
        $relations = [];
        $now = now();

        $this->info('正在收集訂單資料...');

        // 1. 從 orderform.distributor_id 收集關聯 (包含 0 = 平台訂單)
        $orderforms = DB::connection('main_db')
            ->table('orderform')
            ->select('id', 'distributor_id')
            ->get();

        foreach ($orderforms as $orderform) {
            $key = $orderform->id . '_' . $orderform->distributor_id;
            $relations[$key] = [
                'orderform_id' => $orderform->id,
                'distributor_id' => $orderform->distributor_id,
                'created_at' => $now
            ];
        }

        $this->info("從 orderform 表收集到 " . count($relations) . " 筆基礎關聯");

        // 2. 從 orderform_product.distributor_id 收集額外供應商資料
        $productDistributors = DB::connection('main_db')
            ->table('orderform_product')
            ->select('orderform_id', 'distributor_id')
            ->distinct()
            ->get();

        $additionalCount = 0;
        foreach ($productDistributors as $product) {
            $key = $product->orderform_id . '_' . $product->distributor_id;
            if (!isset($relations[$key])) {
                $relations[$key] = [
                    'orderform_id' => $product->orderform_id,
                    'distributor_id' => $product->distributor_id,
                    'created_at' => $now
                ];
                $additionalCount++;
            }
        }

        $this->info("從 orderform_product 表額外收集到 {$additionalCount} 筆關聯");

        return array_values($relations);
    }

    /**
     * 顯示 DRY RUN 結果
     */
    private function displayDryRunResults($relations)
    {
        $this->info('=== DRY RUN 結果 ===');

        // 統計資料
        $distributorStats = [];
        foreach ($relations as $relation) {
            $distributorId = $relation['distributor_id'];
            if (!isset($distributorStats[$distributorId])) {
                $distributorStats[$distributorId] = 0;
            }
            $distributorStats[$distributorId]++;
        }

        $this->table(
            ['供應商ID', '訂單數量', '說明'],
            collect($distributorStats)->map(function ($count, $distributorId) {
                $description = $distributorId == 0 ? '平台訂單' : '供應商訂單';
                return [$distributorId, $count, $description];
            })->toArray()
        );

        // 顯示前10筆範例資料
        $this->info('前10筆範例資料：');
        $this->table(
            ['訂單ID', '供應商ID'],
            array_slice($relations, 0, 10)
        );
    }

    /**
     * 批量插入關聯資料
     */
    private function insertRelations($relations, $batchSize)
    {
        $total = count($relations);
        $processed = 0;

        $this->info("開始批量插入，批次大小：{$batchSize}");

        $progressBar = $this->output->createProgressBar($total);
        $progressBar->start();

        foreach (array_chunk($relations, $batchSize) as $batch) {
            DB::connection('main_db')->table('orderform_distributors')->insert($batch);
            $processed += count($batch);
            $progressBar->advance(count($batch));
        }

        $progressBar->finish();
        $this->newLine();
        $this->info("成功插入 {$processed} 筆關聯資料");
    }

    /**
     * 回滾遷移
     */
    private function rollback()
    {
        $this->warn('開始回滾遷移...');

        $count = DB::connection('main_db')->table('orderform_distributors')->count();

        if ($count == 0) {
            $this->info('關聯表為空，無需回滾');
            return Command::SUCCESS;
        }

        if (!$this->confirm("確定要刪除關聯表中的 {$count} 筆資料嗎？")) {
            $this->info('回滾已取消');
            return Command::FAILURE;
        }

        DB::connection('main_db')->table('orderform_distributors')->truncate();
        $this->info("已成功刪除 {$count} 筆關聯資料");

        return Command::SUCCESS;
    }

    /**
     * 驗證遷移結果
     */
    private function verify()
    {
        $this->info('開始驗證遷移結果...');

        // 檢查總訂單數
        $totalOrders = DB::connection('main_db')->table('orderform')->count();
        $migratedOrders = DB::connection('main_db')
            ->table('orderform_distributors')
            ->distinct('orderform_id')
            ->count();

        $this->info("總訂單數：{$totalOrders}");
        $this->info("已遷移訂單數：{$migratedOrders}");

        if ($totalOrders != $migratedOrders) {
            $this->error("警告：有 " . ($totalOrders - $migratedOrders) . " 筆訂單未遷移！");
        } else {
            $this->info("✓ 所有訂單都已正確遷移");
        }

        // 檢查平台訂單
        $platformOrders = DB::connection('main_db')
            ->table('orderform_distributors')
            ->where('distributor_id', 0)
            ->count();
        $this->info("平台訂單關聯數：{$platformOrders}");

        // 檢查供應商訂單
        $vendorOrders = DB::connection('main_db')
            ->table('orderform_distributors')
            ->where('distributor_id', '>', 0)
            ->count();
        $this->info("供應商訂單關聯數：{$vendorOrders}");

        // 檢查重複資料
        $duplicates = DB::connection('main_db')
            ->table('orderform_distributors')
            ->select('orderform_id', 'distributor_id')
            ->groupBy('orderform_id', 'distributor_id')
            ->havingRaw('COUNT(*) > 1')
            ->count();

        if ($duplicates > 0) {
            $this->error("警告：發現 {$duplicates} 筆重複關聯資料！");
        } else {
            $this->info("✓ 沒有重複的關聯資料");
        }

        $this->info('驗證完成');
        return Command::SUCCESS;
    }
}
