<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;

class OrderformProduct extends Model
{
    protected $connection = 'main_db';
    protected $table = 'orderform_product';
    protected $primaryKey = 'id';
    public $timestamps = false; // orderform_product 表沒有 timestamps

    protected $guarded = ['id'];

    protected $casts = [
        'price' => 'integer',
        'num' => 'integer',
        'total' => 'integer',
        'info_id' => 'integer',
        'type_id' => 'integer',
        'is_registrable' => 'boolean',
        'deal_position' => 'boolean',
        'pre_buy' => 'boolean',
        'pre_buy_num' => 'integer',
        'product_cate' => 'boolean',
        'bonus_model_id' => 'integer',
        'use_ad' => 'boolean',
        'distributor_id' => 'integer',
        'vip_type_reward' => 'integer',
        'vip_type_require' => 'integer',
        'deduct_invest' => 'integer',
        'deduct_consumption' => 'integer',
        'price_cv' => 'decimal:8',
        'price_supplier' => 'decimal:8',
        'supplier_bonus' => 'boolean'
    ];

    /**
     * 訂單關聯
     */
    public function orderform()
    {
        return $this->belongsTo(Orderform::class, 'orderform_id', 'id');
    }

    /**
     * 供應商關聯
     */
    public function distributor()
    {
        return $this->belongsTo(Account::class, 'distributor_id', 'id');
    }

    /**
     * 範圍查詢：根據訂單ID篩選
     */
    public function scopeByOrderform($query, $orderformId)
    {
        return $query->where('orderform_id', $orderformId);
    }

    /**
     * 範圍查詢：根據供應商ID篩選
     */
    public function scopeByDistributor($query, $distributorId)
    {
        return $query->where('distributor_id', $distributorId);
    }

    /**
     * 範圍查詢：平台商品 (distributor_id = 0)
     */
    public function scopePlatformProducts($query)
    {
        return $query->where('distributor_id', 0);
    }

    /**
     * 範圍查詢：供應商商品 (distributor_id > 0)
     */
    public function scopeVendorProducts($query)
    {
        return $query->where('distributor_id', '>', 0);
    }

    /**
     * 範圍查詢：根據商品類型篩選
     */
    public function scopeByProductCate($query, $productCate)
    {
        return $query->where('product_cate', $productCate);
    }

    /**
     * 範圍查詢：投資類商品
     */
    public function scopeInvestmentProducts($query)
    {
        return $query->where('product_cate', 1);
    }

    /**
     * 範圍查詢：消費類商品
     */
    public function scopeConsumptionProducts($query)
    {
        return $query->where('product_cate', 2);
    }

    /**
     * 靜態方法：取得訂單的所有供應商ID
     */
    public static function getDistributorIdsByOrderform($orderformId)
    {
        return self::where('orderform_id', $orderformId)
            ->distinct()
            ->pluck('distributor_id')
            ->toArray();
    }

    /**
     * 靜態方法：取得供應商在指定訂單中的商品
     */
    public static function getProductsByDistributorAndOrderform($distributorId, $orderformId)
    {
        return self::where('distributor_id', $distributorId)
            ->where('orderform_id', $orderformId)
            ->get();
    }

    /**
     * 靜態方法：計算訂單中指定供應商的商品總額
     */
    public static function getTotalByDistributorAndOrderform($distributorId, $orderformId)
    {
        return self::where('distributor_id', $distributorId)
            ->where('orderform_id', $orderformId)
            ->sum('total');
    }

    /**
     * 靜態方法：檢查訂單是否包含指定供應商的商品
     */
    public static function orderHasDistributorProducts($orderformId, $distributorId)
    {
        return self::where('orderform_id', $orderformId)
            ->where('distributor_id', $distributorId)
            ->exists();
    }
}
