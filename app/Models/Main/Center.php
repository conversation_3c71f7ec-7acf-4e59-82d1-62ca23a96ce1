<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Center extends Model
{
    // connection
    protected $connection = 'main_db';

    // table name
    protected $table = 'centers';

    // guarded
    protected $guarded = ['id'];


    // casts
    protected $casts = [
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /* -----------------------------------------------------------------
     |  Relationships
     | -----------------------------------------------------------------
     */
    public function level()
    {
        return $this->belongsTo(CenterLevel::class, 'center_level_id');
    }

    public function staff()
    {
        return $this->hasMany(CenterStaff::class, 'center_id');
    }

    public function activeStaff()
    {
        return $this->staff()->active();
    }

    public function roles()
    {
        return $this->belongsToMany(
            CenterRole::class,
            'center_staff',
            'center_id',
            'role_id'
        )->withPivot(['id', 'account_id', 'start_at', 'end_at', 'note', 'active_flag', 'deleted_at'])
            ->withTimestamps();
    }

    /* -----------------------------------------------------------------
     |  Scopes
     | -----------------------------------------------------------------
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 1);
    }

    public function scopeWithRole(Builder $query, string $roleCode): Builder
    {
        return $query->whereHas('staff.role', function (Builder $q) use ($roleCode) {
            $q->where('code', $roleCode);
        });
    }

    public function scopeWithAccountRole(Builder $query, int $accountId, string $roleCode): Builder
    {
        return $query->whereHas('staff', function (Builder $q) use ($accountId, $roleCode) {
            $q->where('account_id', $accountId)
                ->whereHas('role', function (Builder $r) use ($roleCode) {
                    $r->where('code', $roleCode);
                })
                ->active();
        });
    }

    /* -----------------------------------------------------------------
     |  Helpers
     | -----------------------------------------------------------------
     */
    public function hasActiveRole(string $roleCode): bool
    {
        return $this->activeStaff()
            ->whereHas('role', fn(Builder $q) => $q->where('code', $roleCode))
            ->exists();
    }
}
