<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;

class Orderform extends Model
{
    protected $connection = 'main_db';
    protected $table = 'orderform';
    protected $primaryKey = 'id';
    public $timestamps = false; // orderform 表使用 create_time 而不是 timestamps

    protected $guarded = ['id'];

    protected $casts = [
        'create_time' => 'integer',
        'report_check_time' => 'integer',
        'receipts_state' => 'boolean',
        'transport_state' => 'boolean',
        'transport_date' => 'date',
        'stock_status' => 'boolean',
        'cancel_date' => 'datetime',
        'over_time' => 'datetime'
    ];

    /**
     * 與供應商的多對多關聯 (透過 orderform_distributors 表)
     */
    public function distributors()
    {
        return $this->belongsToMany(
            Account::class, // 假設供應商是 Account 模型
            'orderform_distributors', // 中間表名稱
            'orderform_id', // 當前模型在中間表的外鍵
            'distributor_id', // 關聯模型在中間表的外鍵
            'id', // 當前模型的主鍵
            'id' // 關聯模型的主鍵
        )->withPivot('created_at');
    }

    /**
     * 訂單商品關聯
     */
    public function orderformProducts()
    {
        return $this->hasMany(OrderformProduct::class, 'orderform_id', 'id');
    }

    /**
     * 會員關聯
     */
    public function user()
    {
        return $this->belongsTo(Account::class, 'user_id', 'id');
    }

    /**
     * 主要供應商關聯 (原本的 distributor_id)
     */
    public function mainDistributor()
    {
        return $this->belongsTo(Account::class, 'distributor_id', 'id');
    }

    /**
     * 範圍查詢：根據供應商篩選訂單
     */
    public function scopeByDistributor($query, $distributorId)
    {
        return $query->whereHas('distributors', function ($q) use ($distributorId) {
            $q->where('distributor_id', $distributorId);
        });
    }

    /**
     * 範圍查詢：多供應商訂單
     */
    public function scopeMultiVendor($query)
    {
        return $query->has('distributors', '>', 1);
    }

    /**
     * 範圍查詢：單一供應商訂單
     */
    public function scopeSingleVendor($query)
    {
        return $query->has('distributors', '=', 1);
    }
}
