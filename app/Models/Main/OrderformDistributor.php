<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;

class OrderformDistributor extends Model
{
    protected $connection = 'main_db';
    protected $table = 'orderform_distributors';
    protected $primaryKey = 'id';
    public $timestamps = false; // 只使用 created_at

    protected $fillable = [
        'orderform_id',
        'distributor_id',
        'created_at'
    ];

    protected $casts = [
        'created_at' => 'datetime'
    ];

    /**
     * 訂單關聯
     */
    public function orderform()
    {
        return $this->belongsTo(Orderform::class, 'orderform_id', 'id');
    }

    /**
     * 供應商關聯
     */
    public function distributor()
    {
        return $this->belongsTo(Account::class, 'distributor_id', 'id');
    }

    /**
     * 範圍查詢：根據訂單ID篩選
     */
    public function scopeByOrderform($query, $orderformId)
    {
        return $query->where('orderform_id', $orderformId);
    }

    /**
     * 範圍查詢：根據供應商ID篩選
     */
    public function scopeByDistributor($query, $distributorId)
    {
        return $query->where('distributor_id', $distributorId);
    }

    /**
     * 範圍查詢：平台訂單 (distributor_id = 0)
     */
    public function scopePlatformOrders($query)
    {
        return $query->where('distributor_id', 0);
    }

    /**
     * 範圍查詢：供應商訂單 (distributor_id > 0)
     */
    public function scopeVendorOrders($query)
    {
        return $query->where('distributor_id', '>', 0);
    }

    /**
     * 靜態方法：批量建立關聯
     */
    public static function createBatch($orderformId, $distributorIds)
    {
        $data = [];
        $now = now();

        foreach ($distributorIds as $distributorId) {
            $data[] = [
                'orderform_id' => $orderformId,
                'distributor_id' => $distributorId,
                'created_at' => $now
            ];
        }

        return self::insert($data);
    }

    /**
     * 靜態方法：取得訂單的所有供應商ID
     */
    public static function getDistributorIdsByOrderform($orderformId)
    {
        return self::where('orderform_id', $orderformId)
            ->pluck('distributor_id')
            ->toArray();
    }

    /**
     * 靜態方法：取得供應商的所有訂單ID
     */
    public static function getOrderformIdsByDistributor($distributorId)
    {
        return self::where('distributor_id', $distributorId)
            ->pluck('orderform_id')
            ->toArray();
    }

    /**
     * 靜態方法：檢查訂單是否包含指定供應商
     */
    public static function orderHasDistributor($orderformId, $distributorId)
    {
        return self::where('orderform_id', $orderformId)
            ->where('distributor_id', $distributorId)
            ->exists();
    }

    /**
     * 靜態方法：刪除訂單的所有供應商關聯
     */
    public static function deleteByOrderform($orderformId)
    {
        return self::where('orderform_id', $orderformId)->delete();
    }

    /**
     * 靜態方法：刪除供應商的所有訂單關聯
     */
    public static function deleteByDistributor($distributorId)
    {
        return self::where('distributor_id', $distributorId)->delete();
    }
}
