<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

/**
 * Class CenterStaff
 * 中心角色任職歷程
 *  - active 判斷: end_at IS NULL (由資料庫 generated column active_flag 支援查詢)
 */
class CenterStaff extends Model
{

    protected $connection = 'main_db';
    protected $table = 'center_staff';

    protected $fillable = [
        'center_id',
        'account_id',
        'role_id',
        'start_at',
        'end_at',
        'profit_percentage'
    ];

    protected $casts = [
        'start_at'         => 'datetime',
        'end_at'           => 'datetime',
        'created_at'       => 'datetime',
        'updated_at'       => 'datetime',
        'active_flag'      => 'boolean',
        'profit_percentage' => 'decimal:2',
    ];

    /* -----------------------------------------------------------------
     |  Relationships
     | -----------------------------------------------------------------
     */

    public function role()
    {
        return $this->belongsTo(CenterRole::class, 'role_id');
    }

    public function center()
    {
        return $this->belongsTo(Center::class, 'center_id');
    }

    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id');
    }

    /* -----------------------------------------------------------------
     |  Scopes
     | -----------------------------------------------------------------
     */

    public function scopeActive(Builder $query): Builder
    {
        return $query->whereNull('end_at');
    }

    public function scopeForCenter(Builder $query, int $centerId): Builder
    {
        return $query->where('center_id', $centerId);
    }

    public function scopeSingleRole(Builder $query, string $roleCode): Builder
    {
        return $query->whereHas('role', function (Builder $q) use ($roleCode) {
            $q->where('code', $roleCode);
        });
    }

    /* -----------------------------------------------------------------
     |  Domain Helpers
     | -----------------------------------------------------------------
     */

    /**
     * 結束任期 (若仍為現役)
     */
    public function endNow(): self
    {
        if ($this->end_at === null) {
            $this->end_at = Carbon::now();
            $this->save();
        }
        return $this;
    }
}
