<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;

/**
 * Class CenterRole
 *
 * 角色字典:
 *  - code: 系統固定代碼 (founder,director,executive_director,lecturer,market,sales)
 *  - is_singleton: 是否同一 center 僅允許一個現役
 *  - is_assignable: 是否允許指派 (保留後續動態停用某角色用)
 */
class CenterRole extends Model
{
    protected $connection = 'main_db';
    protected $table = 'center_roles';

    public const CODE_REGIONAL_CENTER    = 'regional_center';
    public const CODE_FOUNDER            = 'founder';
    public const CODE_DIRECTOR           = 'director';
    public const CODE_EXEC_DIRECTOR      = 'executive_director';
    public const CODE_LECTURER           = 'lecturer';
    public const CODE_MARKET             = 'market';
    public const CODE_SALES              = 'sales';

    protected $fillable = [
        'code',
        'name',
        'is_singleton',
        'is_assignable'
    ];

    protected $casts = [
        'is_singleton'  => 'boolean',
        'is_assignable' => 'boolean',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
    ];

    /* -----------------------------------------------------------------
     |  Relationships
     | -----------------------------------------------------------------
     */

    public function staff()
    {
        return $this->hasMany(CenterStaff::class, 'role_id');
    }

    /* -----------------------------------------------------------------
     |  Scopes
     | -----------------------------------------------------------------
     */

    public function scopeAssignable($query)
    {
        return $query->where('is_assignable', 1);
    }

    public function scopeSingleton($query)
    {
        return $query->where('is_singleton', 1);
    }

    /* -----------------------------------------------------------------
     |  Helpers
     | -----------------------------------------------------------------
     */

    /**
     * 取得所有內建角色代碼陣列
     */
    public static function allCodes(): array
    {
        return [
            self::CODE_REGIONAL_CENTER,
            self::CODE_FOUNDER,
            self::CODE_DIRECTOR,
            self::CODE_EXEC_DIRECTOR,
            self::CODE_LECTURER,
            self::CODE_MARKET,
            self::CODE_SALES,
        ];
    }

    /**
     * 快速判斷是否單例角色
     */
    public function isSingleton(): bool
    {
        return (bool) $this->is_singleton;
    }
}
