<?php

namespace App\Repositories\Main;

use App\Models\Main\OrderformProduct;

class OrderformProductRepository
{
    /**
     * 根據訂單ID篩選商品
     */
    public function getByOrderform($orderformId)
    {
        return OrderformProduct::byOrderform($orderformId)->get();
    }

    /**
     * 根據供應商ID篩選商品
     */
    public function getByDistributor($distributorId)
    {
        return OrderformProduct::byDistributor($distributorId)->get();
    }

    /**
     * 取得平台商品 (distributor_id = 0)
     */
    public function getPlatformProducts()
    {
        return OrderformProduct::platformProducts()->get();
    }

    /**
     * 取得供應商商品 (distributor_id > 0)
     */
    public function getVendorProducts()
    {
        return OrderformProduct::vendorProducts()->get();
    }

    /**
     * 根據商品類型篩選
     */
    public function getByProductCate($productCate)
    {
        return OrderformProduct::byProductCate($productCate)->get();
    }

    /**
     * 取得投資類商品
     */
    public function getInvestmentProducts()
    {
        return OrderformProduct::investmentProducts()->get();
    }

    /**
     * 取得消費類商品
     */
    public function getConsumptionProducts()
    {
        return OrderformProduct::consumptionProducts()->get();
    }

    /**
     * 取得訂單的所有供應商ID
     */
    public function getDistributorIdsByOrderform($orderformId)
    {
        return OrderformProduct::getDistributorIdsByOrderform($orderformId);
    }

    /**
     * 取得供應商在指定訂單中的商品
     */
    public function getProductsByDistributorAndOrderform($distributorId, $orderformId)
    {
        return OrderformProduct::getProductsByDistributorAndOrderform($distributorId, $orderformId);
    }

    /**
     * 計算訂單中指定供應商的商品總額
     */
    public function getTotalByDistributorAndOrderform($distributorId, $orderformId)
    {
        return OrderformProduct::getTotalByDistributorAndOrderform($distributorId, $orderformId);
    }

    /**
     * 檢查訂單是否包含指定供應商的商品
     */
    public function orderHasDistributorProducts($orderformId, $distributorId)
    {
        return OrderformProduct::orderHasDistributorProducts($orderformId, $distributorId);
    }

    /**
     * 建立新商品記錄
     */
    public function create(array $data)
    {
        return OrderformProduct::create($data);
    }

    /**
     * 根據ID取得商品
     */
    public function getById($id)
    {
        return OrderformProduct::find($id);
    }

    /**
     * 更新商品記錄
     */
    public function update($id, array $data)
    {
        $product = OrderformProduct::find($id);
        if ($product) {
            return $product->update($data);
        }
        return false;
    }

    /**
     * 刪除商品記錄
     */
    public function delete($id)
    {
        $product = OrderformProduct::find($id);
        if ($product) {
            return $product->delete();
        }
        return false;
    }

    /**
     * 取得所有商品
     */
    public function getAll()
    {
        return OrderformProduct::all();
    }
}
