<?php

namespace App\Repositories\Main;

use App\Models\Main\Orderform;

class OrderformRepository
{
    /**
     * 取得會員訂單數
     * @param int $userId
     * @return int
     */
    public function getOrderCountByUserId($userId)
    {
        return Orderform::where('user_id', $userId)->count();
    }

    /**
     * 根據供應商篩選訂單
     * @param int $distributorId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getOrdersByDistributor($distributorId)
    {
        return Orderform::byDistributor($distributorId)->get();
    }

    /**
     * 取得多供應商訂單
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getMultiVendorOrders()
    {
        return Orderform::multiVendor()->get();
    }

    /**
     * 取得單一供應商訂單
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getSingleVendorOrders()
    {
        return Orderform::singleVendor()->get();
    }

    /**
     * 根據訂單ID取得訂單詳情
     * @param int $orderformId
     * @return Orderform|null
     */
    public function getOrderById($orderformId)
    {
        return Orderform::find($orderformId);
    }

    /**
     * 根據ID取得訂單詳情 (別名方法)
     * @param int $id
     * @return Orderform|null
     */
    public function getById($id)
    {
        return $this->getOrderById($id);
    }

    /**
     * 根據訂單編號取得訂單詳情
     * @param string $orderNumber
     * @return Orderform|null
     */
    public function getOrderByNumber($orderNumber)
    {
        return Orderform::where('order_number', $orderNumber)->first();
    }

    /**
     * 根據會員ID取得訂單
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getOrdersByUserId($userId)
    {
        return Orderform::where('user_id', $userId)->get();
    }

    /**
     * 根據狀態取得訂單
     * @param string|array $status
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getOrdersByStatus($status)
    {
        if (is_array($status)) {
            return Orderform::whereIn('status', $status)->get();
        }
        return Orderform::where('status', $status)->get();
    }

    /**
     * 根據日期範圍取得訂單
     * @param int $startTime
     * @param int $endTime
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getOrdersByDateRange($startTime, $endTime)
    {
        return Orderform::whereBetween('create_time', [$startTime, $endTime])->get();
    }

    /**
     * 建立新訂單
     * @param array $data
     * @return Orderform
     */
    public function create(array $data)
    {
        return Orderform::create($data);
    }

    /**
     * 更新訂單
     * @param int $orderformId
     * @param array $data
     * @return bool
     */
    public function update($orderformId, array $data)
    {
        $orderform = Orderform::find($orderformId);
        if ($orderform) {
            return $orderform->update($data);
        }
        return false;
    }

    /**
     * 刪除訂單
     * @param int $orderformId
     * @return bool
     */
    public function delete($orderformId)
    {
        $orderform = Orderform::find($orderformId);
        if ($orderform) {
            return $orderform->delete();
        }
        return false;
    }

    /**
     * 根據訂單編號模式取得最新訂單
     * @param string $pattern
     * @return Orderform|null
     */
    public function getLatestOrderByPattern($pattern)
    {
        return Orderform::where('order_number', 'like', $pattern)
            ->orderBy('id', 'desc')
            ->first();
    }
}
