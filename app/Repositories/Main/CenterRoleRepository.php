<?php

namespace App\Repositories\Main;

use App\Models\Main\CenterRole;

class CenterRoleRepository
{
    public function __construct(protected CenterRole $centerRole) {}

    /**
     * 取得所有角色選項
     */
    public function getCenterRoles()
    {
        return $this->centerRole->orderBy('id')->get();
    }

    /**
     * 根據代碼查找角色
     */
    public function findByCode(string $code): ?CenterRole
    {
        return $this->centerRole->where('code', $code)->first();
    }

    /**
     * 根據代碼查找可指派的角色
     */
    public function findAssignableByCode(string $code): ?CenterRole
    {
        return $this->centerRole->where('code', $code)
            ->where('is_assignable', 1)
            ->first();
    }

    /**
     * 根據代碼查找單例角色
     */
    public function findSingletonByCode(string $code): ?CenterRole
    {
        return $this->centerRole->where('code', $code)
            ->where('is_singleton', 1)
            ->first();
    }
}
