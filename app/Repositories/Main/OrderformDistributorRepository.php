<?php

namespace App\Repositories\Main;

use App\Models\Main\OrderformDistributor;

class OrderformDistributorRepository
{
    /**
     * 根據訂單ID篩選關聯
     * @param int $orderformId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByOrderform($orderformId)
    {
        return OrderformDistributor::byOrderform($orderformId)->get();
    }

    /**
     * 根據供應商ID篩選關聯
     * @param int $distributorId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByDistributor($distributorId)
    {
        return OrderformDistributor::byDistributor($distributorId)->get();
    }

    /**
     * 取得平台訂單關聯 (distributor_id = 0)
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPlatformOrders()
    {
        return OrderformDistributor::platformOrders()->get();
    }

    /**
     * 取得供應商訂單關聯 (distributor_id > 0)
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getVendorOrders()
    {
        return OrderformDistributor::vendorOrders()->get();
    }

    /**
     * 批量建立關聯
     * @param int $orderformId
     * @param array $distributorIds
     * @return bool
     */
    public function createBatch($orderformId, $distributorIds)
    {
        return OrderformDistributor::createBatch($orderformId, $distributorIds);
    }

    /**
     * 取得訂單的所有供應商ID
     * @param int $orderformId
     * @return array
     */
    public function getDistributorIdsByOrderform($orderformId)
    {
        return OrderformDistributor::getDistributorIdsByOrderform($orderformId);
    }

    /**
     * 取得供應商的所有訂單ID
     * @param int $distributorId
     * @return array
     */
    public function getOrderformIdsByDistributor($distributorId)
    {
        return OrderformDistributor::getOrderformIdsByDistributor($distributorId);
    }

    /**
     * 檢查訂單是否包含指定供應商
     * @param int $orderformId
     * @param int $distributorId
     * @return bool
     */
    public function orderHasDistributor($orderformId, $distributorId)
    {
        return OrderformDistributor::orderHasDistributor($orderformId, $distributorId);
    }

    /**
     * 刪除訂單的所有供應商關聯
     * @param int $orderformId
     * @return int
     */
    public function deleteByOrderform($orderformId)
    {
        return OrderformDistributor::deleteByOrderform($orderformId);
    }

    /**
     * 刪除供應商的所有訂單關聯
     * @param int $distributorId
     * @return int
     */
    public function deleteByDistributor($distributorId)
    {
        return OrderformDistributor::deleteByDistributor($distributorId);
    }

    /**
     * 取得關聯統計資料
     * @return array
     */
    public function getStatistics()
    {
        $stats = [];

        // 總關聯數
        $stats['total_relations'] = OrderformDistributor::count();

        // 平台訂單數
        $stats['platform_orders'] = OrderformDistributor::platformOrders()->count();

        // 供應商訂單數
        $stats['vendor_orders'] = OrderformDistributor::vendorOrders()->count();

        // 多供應商訂單數
        $stats['multi_vendor_orders'] = OrderformDistributor::select('orderform_id')
            ->groupBy('orderform_id')
            ->havingRaw('COUNT(DISTINCT distributor_id) > 1')
            ->count();

        // 供應商數量
        $stats['distributor_count'] = OrderformDistributor::distinct('distributor_id')->count();

        return $stats;
    }

    /**
     * 取得供應商訂單統計
     * @param int $distributorId
     * @return array
     */
    public function getDistributorStatistics($distributorId)
    {
        $stats = [];

        // 該供應商的訂單數
        $stats['order_count'] = OrderformDistributor::byDistributor($distributorId)->count();

        // 該供應商參與的多供應商訂單數
        $multiVendorOrderIds = OrderformDistributor::select('orderform_id')
            ->groupBy('orderform_id')
            ->havingRaw('COUNT(DISTINCT distributor_id) > 1')
            ->pluck('orderform_id');

        $stats['multi_vendor_count'] = OrderformDistributor::byDistributor($distributorId)
            ->whereIn('orderform_id', $multiVendorOrderIds)
            ->count();

        return $stats;
    }

    /**
     * 建立新關聯
     * @param array $data
     * @return OrderformDistributor
     */
    public function create(array $data)
    {
        return OrderformDistributor::create($data);
    }

    /**
     * 取得所有關聯
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAll()
    {
        return OrderformDistributor::all();
    }

    /**
     * 根據ID取得關聯
     * @param int $id
     * @return OrderformDistributor|null
     */
    public function getById($id)
    {
        return OrderformDistributor::find($id);
    }
}
