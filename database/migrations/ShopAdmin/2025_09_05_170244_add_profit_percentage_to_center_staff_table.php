<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->table('center_staff', function (Blueprint $table) {
            $table->decimal('profit_percentage', 4, 1)
                ->default(0.0)
                ->comment('分潤百分比(0.0-100.0)，用於多人角色分潤計算')
                ->after('note');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->table('center_staff', function (Blueprint $table) {
            $table->dropColumn('profit_percentage');
        });
    }
};
