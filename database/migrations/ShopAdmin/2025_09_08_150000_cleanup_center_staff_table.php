<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 清理 center_staff 表：移除 deleted_at 和 note 欄位
     *
     * @return void
     */
    public function up()
    {
        // 先刪除依賴 deleted_at 的 active_flag
        Schema::connection('main_db')->table('center_staff', function (Blueprint $table) {
            $table->dropColumn('active_flag');
        });

        // 然後移除 deleted_at 和 note 欄位
        Schema::connection('main_db')->table('center_staff', function (Blueprint $table) {
            $table->dropSoftDeletes();
            $table->dropColumn('note');
        });

        // 最後重新建立 active_flag，只檢查 end_at
        Schema::connection('main_db')->table('center_staff', function (Blueprint $table) {
            $table->tinyInteger('active_flag')
                ->storedAs("(CASE WHEN `end_at` IS NULL THEN 1 ELSE 0 END)")
                ->comment('是否現役(產生欄): 1=現役(end_at IS NULL), 0=已結束任期');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 恢復 deleted_at 和 note 欄位
        Schema::connection('main_db')->table('center_staff', function (Blueprint $table) {
            $table->softDeletes();
            $table->string('note', 255)->nullable()->comment('備註');
        });

        // 恢復原來的 active_flag 定義
        Schema::connection('main_db')->table('center_staff', function (Blueprint $table) {
            $table->dropColumn('active_flag');
        });

        Schema::connection('main_db')->table('center_staff', function (Blueprint $table) {
            $table->tinyInteger('active_flag')
                ->storedAs("(CASE WHEN `end_at` IS NULL AND `deleted_at` IS NULL THEN 1 ELSE 0 END)")
                ->comment('是否現役(產生欄)');
        });
    }
};
