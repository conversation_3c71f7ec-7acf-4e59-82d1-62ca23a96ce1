<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('orderform_distributors', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('orderform_id')->comment('訂單ID');
            $table->unsignedInteger('distributor_id')->comment('供應商ID，0表示平台訂單');
            $table->timestamp('created_at')->useCurrent()->comment('建立時間');

            // 建立唯一索引，確保同一訂單的同一供應商不會重複
            $table->unique(['orderform_id', 'distributor_id'], 'unique_orderform_distributor');

            // 建立索引提升查詢效能
            $table->index('orderform_id', 'idx_orderform_id');
            $table->index('distributor_id', 'idx_distributor_id');

            // 外鍵約束 (如果需要的話，可以根據實際情況調整)
            // $table->foreign('orderform_id')->references('id')->on('orderform')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('orderform_distributors');
    }
};
