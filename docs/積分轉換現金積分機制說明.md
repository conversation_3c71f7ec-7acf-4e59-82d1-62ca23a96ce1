# 積分轉換現金積分機制說明

## 概述

本文檔說明 `BonusHelper.php` 中 `send_by_cal` 方法關於轉換現金積分的機制，包括觸發條件、執行流程和相關程式碼位置。

## 主要轉換現金積分的位置

### 1. 積分自動轉換機制 (第 598 行)

在 [`arrange_final_data`](../app/Services/pattern/BonusHelper.php:514) 方法中：

```php
/*處理「積分自動轉換」*/
$this->check_point_limit($user_id);
```

這是整個轉換機制的入口點，會對每個會員檢查是否需要進行積分轉換。

### 2. 核心轉換邏輯 (第 1099-1159 行)

在 [`check_point_limit`](../app/Services/pattern/BonusHelper.php:1099) 方法中，這是主要的轉換現金積分邏輯：

#### 2.1 圓滿點數檢查 (第 1118-1146 行)

當增值積分價值變化超過各種圓滿點數時，會觸發轉換：

```php
/*逐個圓滿點數檢查增值量*/
foreach (self::$limit_type_to_definition as $limit_type => $definition) {
    if ($pi_value_change <= 0) {
        return;
    } /*已無待檢查價值變化量*/

    $limit_column = $definition[0];                                     /*對應圓滿點數欄位*/
    $limit_name = $definition[1];                                       /*對應圓滿點數中文名稱*/
    $user_limit = $this->final_user_status[$target_id][$limit_column];  /*對應圓滿點數持有數量*/

    if ($user_limit > 0) { /*有圓滿點數*/
        // 扣除指定圓滿點數並檢查是否達圓滿
    }
}
```

#### 2.2 達圓滿拋轉 (第 1134-1142 行)

當達到圓滿條件時，會將增值積分轉換為現金積分：

```php
if ($pi_value_change >= $user_limit) { /*如果增值量大於等於持有圓滿點數(達圓滿)*/
    /*達圓滿金額 = 原持有增值積分價值 + 剩餘功德圓滿點數*/
    $reach_num = $point_increasable_ori_value + $user_limit;
    if ($reach_num > 0 && $pi_value_new > 0) {
        /*只要圓滿就先拋轉積分*/
        $num = $reach_num / $pi_value_new;
        $this->point_increasable_to_point($target_id, $num, $limit_name . '達可增值上限拋轉現金積分');
    }
}
```

#### 2.3 全部拋轉 (第 1154-1158 行)

當圓滿點數都檢查完後，如果還有剩餘價值變化，會將所有增值積分轉換為現金積分：

```php
/*計算全部增值積分價值*/
$point_increasable = $this->final_user_status[$target_id]['point_increasable'];
if ($point_increasable > 0) {
    /*拋轉現金積分*/
    $this->point_increasable_to_point($target_id, $point_increasable, '達可增值上限拋轉現金積分');
}
```

### 3. 實際轉換執行 (第 1249-1262 行)

在 [`point_increasable_to_point`](../app/Services/pattern/BonusHelper.php:1249) 方法中執行實際的轉換：

```php
private function point_increasable_to_point(int $target_id, float $point_increasable, string $transfer_msg)
{
    $pi_value_ori = $this->get_pi_value();      /*增值積分現值*/
    $pi_value_new = $this->get_pi_value_new();  /*增值積分調整後現值*/

    /*設定添加「現金積分」*/
    $point_value = $point_increasable * $pi_value_new;
    $this->set_final_points_record($target_id, $transfer_msg, $point_value);

    /*設定減少「增值積分」*/
    $this->set_final_points_increasing_record($target_id, $transfer_msg, (-1.0 * $point_increasable));

    $this->add_pi_pool(-1.0 * $point_value);                /*因增值積分減少，「增值積分資金池」異動CV金額需減少*/
    $this->pi_change -= $point_increasable * $pi_value_ori; /*因增值積分減少，「增值積分」異動CV金額需減少*/
}
```

### 4. 自動升級借貸機制 (第 1180-1243 行)

在 [`auto_partner_levelup`](../app/Services/pattern/BonusHelper.php:1162) 方法中，當會員設定自動升級時，也會處理積分轉換：

```php
if ($auto_partner == 2 && $partner_level_id > 0) {
    // 檢查是否有下一階級
    // 計算升級差額
    // 如果增值積分價值足夠升級，則扣除相應的增值積分
    if ($all_num > $diff_contribution && $diff_contribution > 0) {
        /*扣除升級者用於投資的增值積分*/
        if ($pi_value_new > 0) {
            $point_increasable = (float)(-1.0 * $diff_contribution / $pi_value_new);
            $this->set_final_points_increasing_record($levelup_user_id, '自動升級合夥人等級', $point_increasable);
        }
    }
}
```

## 轉換觸發條件

1. **增值積分價值超過圓滿點數限制**

    - 功德圓滿點數 (`increasing_limit_invest`)
    - 消費圓滿點數 (`increasing_limit_consumption`)
    - 其他圓滿點數 (`increasing_limit_other`)

2. **達到可增值上限**

    - 當所有圓滿點數都用完後，剩餘的增值積分會全部轉換為現金積分

3. **自動升級合夥人等級時需要投資金額**
    - 會員設定自動升級 (`auto_partner == 2`)
    - 有合夥等級 (`partner_level_id > 0`)
    - 增值積分價值足夠升級所需金額

## 轉換流程圖

```mermaid
graph TD
    A[send_by_cal] --> B[arrange_final_data]
    B --> C[check_point_limit]
    C --> D{是否有價值變化}
    D -->|是| E[auto_partner_levelup]
    E --> F{是否自動升級}
    F -->|是| G[處理自動升級轉換]
    F -->|否| H[檢查圓滿點數]
    H --> I{圓滿點數 > 0}
    I -->|是| J[扣除圓滿點數]
    J --> K{是否達圓滿}
    K -->|是| L[拋轉現金積分]
    K -->|否| M[繼續檢查下一種圓滿點數]
    I -->|否| N{還有價值變化}
    N -->|是| O[全部增值積分拋轉現金積分]
    L --> P[point_increasable_to_point]
    O --> P
    P --> Q[設定現金積分記錄]
    P --> R[設定增值積分記錄]
    Q --> S[更新資金池]
    R --> S
```

## 相關資料結構

### 圓滿點數類型定義

```php
public static $limit_type_to_definition = [
    '1' => ['increasing_limit_invest', '功德圓滿點數'],
    '3' => ['increasing_limit_other', '其他圓滿點數'],
    '2' => ['increasing_limit_consumption', '消費圓滿點數'],
];
```

### 最終紀錄結構

-   `final_points_record`: 現金積分變動紀錄
-   `final_points_increasing_record`: 增值積分變動紀錄
-   `final_increasing_limit_record`: 圓滿點數變動紀錄

## 重要注意事項

1. **系統帳戶免檢查**: 系統帳戶、月分紅帳戶不進行自動轉換檢查
2. **價值計算**: 轉換時使用調整後的增值積分現值 (`pi_value_new`)
3. **資金池同步**: 每次轉換都會同步更新增值積分資金池
4. **借貸機制**: 自動升級時可能產生負債，後續投資會優先歸還借貸

這個機制確保當會員的增值積分價值超過其圓滿點數限制時，會自動將超出的部分轉換為現金積分，以維持系統的平衡。
