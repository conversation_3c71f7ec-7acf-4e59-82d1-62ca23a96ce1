/**
 * 中心管理前端用戶體驗增強系統
 * 提供載入狀態、通知系統、鍵盤快捷鍵等功能
 */

class CenterUXEnhancements {
    constructor() {
        this.loadingStates = new Map();
        this.notifications = [];
        this.keyboardShortcuts = new Map();
        this.init();
    }

    /**
     * 初始化 UX 增強功能
     */
    init() {
        this.setupLoadingIndicators();
        this.setupNotificationSystem();
        this.setupKeyboardShortcuts();
        this.setupFormInteractions();
        this.setupProgressIndicators();
        this.bindEvents();
    }

    /**
     * 設置載入狀態指示器
     */
    setupLoadingIndicators() {
        // 全域載入遮罩
        this.createGlobalLoadingOverlay();

        // 按鈕載入狀態管理
        this.setupButtonLoadingStates();

        // 表單載入狀態
        this.setupFormLoadingStates();

        // AJAX 請求載入指示器
        this.setupAjaxLoadingIndicators();
    }

    /**
     * 創建全域載入遮罩
     */
    createGlobalLoadingOverlay() {
        if ($("#global-loading-overlay").length === 0) {
            const overlayHtml = `
                <div id="global-loading-overlay" class="loading-overlay" style="display: none;">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">載入中...</span>
                        </div>
                        <div class="loading-text mt-3">處理中，請稍候...</div>
                    </div>
                </div>
            `;
            $("body").append(overlayHtml);
        }
    }

    /**
     * 顯示全域載入狀態
     */
    showGlobalLoading(message = "處理中，請稍候...") {
        $("#global-loading-overlay .loading-text").text(message);
        $("#global-loading-overlay").fadeIn(200);
    }

    /**
     * 隱藏全域載入狀態
     */
    hideGlobalLoading() {
        $("#global-loading-overlay").fadeOut(200);
    }

    /**
     * 設置按鈕載入狀態管理
     */
    setupButtonLoadingStates() {
        // 為所有提交按鈕添加載入狀態
        $(document).on("click", "[data-loading-button]", (e) => {
            const $button = $(e.target);
            const loadingText = $button.data("loading-text") || "處理中...";
            this.setButtonLoading($button, true, loadingText);
        });
    }

    /**
     * 設置按鈕載入狀態
     */
    setButtonLoading($button, isLoading, loadingText = "處理中...") {
        const buttonId = $button.attr("id") || "btn-" + Date.now();

        if (isLoading) {
            // 儲存原始狀態
            this.loadingStates.set(buttonId, {
                originalText: $button.html(),
                originalDisabled: $button.prop("disabled"),
            });

            // 設置載入狀態
            $button
                .prop("disabled", true)
                .html(
                    `<i class="bi bi-hourglass-split spinner-icon"></i> ${loadingText}`
                );

            // 添加載入動畫
            $button.addClass("btn-loading");
        } else {
            // 恢復原始狀態
            const originalState = this.loadingStates.get(buttonId);
            if (originalState) {
                $button
                    .prop("disabled", originalState.originalDisabled)
                    .html(originalState.originalText);
                this.loadingStates.delete(buttonId);
            }

            // 移除載入動畫
            $button.removeClass("btn-loading");
        }
    }

    /**
     * 設置表單載入狀態
     */
    setupFormLoadingStates() {
        // 表單提交時顯示載入狀態
        $(document).on("submit", "form[data-loading-form]", (e) => {
            const $form = $(e.target);
            this.setFormLoading($form, true);
        });
    }

    /**
     * 設置表單載入狀態
     */
    setFormLoading($form, isLoading) {
        if (isLoading) {
            $form.addClass("form-loading");
            $form
                .find("input, select, textarea, button")
                .prop("disabled", true);

            // 添加載入指示器到表單
            if ($form.find(".form-loading-indicator").length === 0) {
                const indicator = `
                    <div class="form-loading-indicator">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                <span class="sr-only">載入中...</span>
                            </div>
                        </div>
                    </div>
                `;
                $form.prepend(indicator);
            }
        } else {
            $form.removeClass("form-loading");
            $form
                .find("input, select, textarea, button")
                .prop("disabled", false);
            $form.find(".form-loading-indicator").remove();
        }
    }

    /**
     * 設置 AJAX 載入指示器
     */
    setupAjaxLoadingIndicators() {
        // 全域 AJAX 載入指示器
        $(document).ajaxStart(() => {
            this.showAjaxLoading();
        });

        $(document).ajaxStop(() => {
            this.hideAjaxLoading();
        });

        // 特定元素的 AJAX 載入指示器
        $(document).on("ajaxStart", "[data-ajax-loading]", (e) => {
            const $element = $(e.target);
            this.showElementLoading($element);
        });

        $(document).on("ajaxComplete", "[data-ajax-loading]", (e) => {
            const $element = $(e.target);
            this.hideElementLoading($element);
        });
    }

    /**
     * 顯示 AJAX 載入指示器
     */
    showAjaxLoading() {
        if ($("#ajax-loading-indicator").length === 0) {
            const indicator = `
                <div id="ajax-loading-indicator" class="ajax-loading-indicator">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="sr-only">載入中...</span>
                    </div>
                </div>
            `;
            $("body").append(indicator);
        }
        $("#ajax-loading-indicator").fadeIn(200);
    }

    /**
     * 隱藏 AJAX 載入指示器
     */
    hideAjaxLoading() {
        $("#ajax-loading-indicator").fadeOut(200);
    }

    /**
     * 顯示元素載入狀態
     */
    showElementLoading($element) {
        const loadingHtml = `
            <div class="element-loading-overlay">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">載入中...</span>
                </div>
            </div>
        `;

        $element.css("position", "relative").append(loadingHtml);
    }

    /**
     * 隱藏元素載入狀態
     */
    hideElementLoading($element) {
        $element.find(".element-loading-overlay").remove();
    }

    /**
     * 設置通知系統
     */
    setupNotificationSystem() {
        // 創建通知容器
        this.createNotificationContainer();

        // 設置通知類型
        this.setupNotificationTypes();
    }

    /**
     * 創建通知容器
     */
    createNotificationContainer() {
        if ($("#notification-container").length === 0) {
            const containerHtml = `
                <div id="notification-container" class="notification-container">
                    <div class="notification-stack"></div>
                </div>
            `;
            $("body").append(containerHtml);
        }
    }

    /**
     * 設置通知類型
     */
    setupNotificationTypes() {
        // 定義通知類型配置
        this.notificationTypes = {
            success: {
                icon: "bi-check-circle-fill",
                color: "text-success",
                bgClass: "notification-success",
            },
            error: {
                icon: "bi-x-circle-fill",
                color: "text-danger",
                bgClass: "notification-error",
            },
            warning: {
                icon: "bi-exclamation-triangle-fill",
                color: "text-warning",
                bgClass: "notification-warning",
            },
            info: {
                icon: "bi-info-circle-fill",
                color: "text-info",
                bgClass: "notification-info",
            },
        };
    }

    /**
     * 顯示通知
     */
    showNotification(type, title, message, options = {}) {
        const defaultOptions = {
            duration: type === "error" ? 8000 : 5000,
            closable: true,
            position: "top-right",
            showProgress: true,
            sound: false,
        };

        const config = { ...defaultOptions, ...options };
        const notificationId = "notification-" + Date.now();

        const notification = {
            id: notificationId,
            type,
            title,
            message,
            config,
            timestamp: new Date(),
        };

        this.notifications.push(notification);
        this.renderNotification(notification);

        // 自動移除通知
        if (config.duration > 0) {
            setTimeout(() => {
                this.removeNotification(notificationId);
            }, config.duration);
        }

        // 播放聲音（如果啟用）
        if (config.sound) {
            this.playNotificationSound(type);
        }

        return notificationId;
    }

    /**
     * 渲染通知
     */
    renderNotification(notification) {
        const iconMap = {
            success: "bi-check-circle-fill text-success",
            error: "bi-x-circle-fill text-danger",
            warning: "bi-exclamation-triangle-fill text-warning",
            info: "bi-info-circle-fill text-info",
        };

        const bgMap = {
            success: "notification-success",
            error: "notification-error",
            warning: "notification-warning",
            info: "notification-info",
        };

        const progressBar = notification.config.showProgress
            ? `<div class="notification-progress">
                <div class="notification-progress-bar" style="animation-duration: ${notification.config.duration}ms;"></div>
            </div>`
            : "";

        const closeButton = notification.config.closable
            ? `<button type="button" class="notification-close" data-notification-id="${notification.id}">
                <i class="bi bi-x"></i>
            </button>`
            : "";

        const notificationHtml = `
            <div id="${notification.id}" class="notification ${
            bgMap[notification.type]
        } notification-enter">
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="bi ${iconMap[notification.type]}"></i>
                    </div>
                    <div class="notification-body">
                        <div class="notification-title">${
                            notification.title
                        }</div>
                        ${
                            notification.message
                                ? `<div class="notification-message">${notification.message}</div>`
                                : ""
                        }
                    </div>
                    ${closeButton}
                </div>
                ${progressBar}
            </div>
        `;

        $("#notification-container .notification-stack").append(
            notificationHtml
        );

        // 觸發進入動畫
        setTimeout(() => {
            $(`#${notification.id}`)
                .removeClass("notification-enter")
                .addClass("notification-show");
        }, 10);
    }

    /**
     * 移除通知
     */
    removeNotification(notificationId) {
        const $notification = $(`#${notificationId}`);
        if ($notification.length > 0) {
            $notification
                .removeClass("notification-show")
                .addClass("notification-exit");

            setTimeout(() => {
                $notification.remove();
                this.notifications = this.notifications.filter(
                    (n) => n.id !== notificationId
                );
            }, 300);
        }
    }

    /**
     * 清除所有通知
     */
    clearAllNotifications() {
        $(".notification")
            .removeClass("notification-show")
            .addClass("notification-exit");

        setTimeout(() => {
            $("#notification-container .notification-stack").empty();
            this.notifications = [];
        }, 300);
    }

    /**
     * 播放通知聲音
     */
    playNotificationSound(type) {
        // 創建音頻上下文（如果支援）
        if (window.AudioContext || window.webkitAudioContext) {
            const audioContext = new (window.AudioContext ||
                window.webkitAudioContext)();

            // 不同類型的通知使用不同頻率
            const frequencies = {
                success: [523.25, 659.25, 783.99], // C5, E5, G5
                error: [220, 196], // A3, G3
                warning: [440, 493.88], // A4, B4
                info: [523.25], // C5
            };

            const freq = frequencies[type] || frequencies.info;

            freq.forEach((frequency, index) => {
                setTimeout(() => {
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.frequency.setValueAtTime(
                        frequency,
                        audioContext.currentTime
                    );
                    oscillator.type = "sine";

                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(
                        0.01,
                        audioContext.currentTime + 0.2
                    );

                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.2);
                }, index * 100);
            });
        }
    }

    /**
     * 設置鍵盤快捷鍵
     */
    setupKeyboardShortcuts() {
        // 註冊預設快捷鍵
        this.registerShortcut(
            "ctrl+s",
            (e) => {
                e.preventDefault();
                this.handleSaveShortcut();
            },
            "儲存表單"
        );

        this.registerShortcut(
            "escape",
            (e) => {
                this.handleEscapeShortcut();
            },
            "取消操作"
        );

        this.registerShortcut(
            "ctrl+z",
            (e) => {
                e.preventDefault();
                this.handleUndoShortcut();
            },
            "復原操作"
        );

        this.registerShortcut(
            "ctrl+shift+z",
            (e) => {
                e.preventDefault();
                this.handleRedoShortcut();
            },
            "重做操作"
        );

        this.registerShortcut(
            "ctrl+/",
            (e) => {
                e.preventDefault();
                this.showKeyboardShortcutsHelp();
            },
            "顯示快捷鍵說明"
        );

        this.registerShortcut(
            "alt+n",
            (e) => {
                e.preventDefault();
                this.handleNewShortcut();
            },
            "新增項目"
        );

        this.registerShortcut(
            "alt+e",
            (e) => {
                e.preventDefault();
                this.handleEditShortcut();
            },
            "編輯項目"
        );

        this.registerShortcut(
            "delete",
            (e) => {
                this.handleDeleteShortcut();
            },
            "刪除項目"
        );

        // 綁定鍵盤事件
        $(document).on("keydown", (e) => {
            this.handleKeyboardEvent(e);
        });
    }

    /**
     * 註冊快捷鍵
     */
    registerShortcut(combination, handler, description) {
        this.keyboardShortcuts.set(combination, {
            handler,
            description,
            enabled: true,
        });
    }

    /**
     * 處理鍵盤事件
     */
    handleKeyboardEvent(e) {
        const combination = this.getKeyCombination(e);
        const shortcut = this.keyboardShortcuts.get(combination);

        if (shortcut && shortcut.enabled) {
            // 檢查是否在輸入元素中
            const isInputElement = $(e.target).is(
                "input, textarea, select, [contenteditable]"
            );

            // 某些快捷鍵在輸入元素中也要生效
            const globalShortcuts = [
                "escape",
                "ctrl+s",
                "ctrl+/",
                "ctrl+z",
                "ctrl+shift+z",
            ];

            if (!isInputElement || globalShortcuts.includes(combination)) {
                shortcut.handler(e);
            }
        }
    }

    /**
     * 獲取按鍵組合
     */
    getKeyCombination(e) {
        const keys = [];

        if (e.ctrlKey) keys.push("ctrl");
        if (e.altKey) keys.push("alt");
        if (e.shiftKey) keys.push("shift");
        if (e.metaKey) keys.push("meta");

        // 特殊鍵
        const specialKeys = {
            8: "backspace",
            9: "tab",
            13: "enter",
            27: "escape",
            32: "space",
            46: "delete",
            37: "left",
            38: "up",
            39: "right",
            40: "down",
        };

        if (specialKeys[e.keyCode]) {
            keys.push(specialKeys[e.keyCode]);
        } else if (e.keyCode >= 65 && e.keyCode <= 90) {
            // 字母鍵
            keys.push(String.fromCharCode(e.keyCode).toLowerCase());
        } else if (e.keyCode >= 48 && e.keyCode <= 57) {
            // 數字鍵
            keys.push(String.fromCharCode(e.keyCode));
        } else {
            // 其他鍵
            keys.push(e.keyCode.toString());
        }

        return keys.join("+");
    }

    /**
     * 快捷鍵處理函數
     */
    handleSaveShortcut() {
        const $form = $("form:visible").first();
        if ($form.length > 0) {
            $form.submit();
            this.showNotification("info", "快捷鍵", "正在儲存表單...");
        }
    }

    handleEscapeShortcut() {
        // 關閉模態框
        $(".modal.show").modal("hide");

        // 清除通知
        this.clearAllNotifications();

        // 取消表單編輯
        if ($("form:visible").length > 0) {
            if (confirm("確定要取消嗎？未儲存的變更將會遺失。")) {
                window.history.back();
            }
        }
    }

    handleUndoShortcut() {
        // 實作復原功能（如果有的話）
        this.showNotification("info", "快捷鍵", "復原功能尚未實作");
    }

    handleRedoShortcut() {
        // 實作重做功能（如果有的話）
        this.showNotification("info", "快捷鍵", "重做功能尚未實作");
    }

    handleNewShortcut() {
        // 導向新增頁面
        const currentPath = window.location.pathname;
        if (currentPath.includes("/center/")) {
            window.location.href = "/admin/center/create";
        }
    }

    handleEditShortcut() {
        // 編輯當前項目
        const $editBtn = $(".btn-edit:visible").first();
        if ($editBtn.length > 0) {
            $editBtn.click();
        }
    }

    handleDeleteShortcut() {
        // 刪除選中項目
        const $deleteBtn = $(".btn-delete:visible").first();
        if ($deleteBtn.length > 0) {
            $deleteBtn.click();
        }
    }

    /**
     * 顯示快捷鍵說明
     */
    showKeyboardShortcutsHelp() {
        let helpContent =
            '<div class="keyboard-shortcuts-help"><h5>鍵盤快捷鍵</h5><ul>';

        this.keyboardShortcuts.forEach((shortcut, combination) => {
            if (shortcut.enabled) {
                const displayCombination = combination
                    .replace("ctrl", "Ctrl")
                    .replace("alt", "Alt")
                    .replace("shift", "Shift")
                    .replace("escape", "Esc")
                    .replace("+", " + ");

                helpContent += `<li><kbd>${displayCombination}</kbd> - ${shortcut.description}</li>`;
            }
        });

        helpContent += "</ul></div>";

        this.showNotification("info", "鍵盤快捷鍵", helpContent, {
            duration: 10000,
            closable: true,
        });
    }

    /**
     * 設置表單互動增強
     */
    setupFormInteractions() {
        // 自動儲存功能
        this.setupAutoSave();

        // 表單變更追蹤
        this.setupFormChangeTracking();

        // 智能焦點管理
        this.setupSmartFocus();

        // 表單進度指示器已在 init() 中的 setupProgressIndicators() 調用
    }

    /**
     * 設置自動儲存
     */
    setupAutoSave() {
        let autoSaveTimer;

        $(document).on(
            "input change",
            "form[data-auto-save] input, form[data-auto-save] textarea, form[data-auto-save] select",
            () => {
                clearTimeout(autoSaveTimer);

                autoSaveTimer = setTimeout(() => {
                    this.performAutoSave();
                }, 30000); // 30秒後自動儲存
            }
        );
    }

    /**
     * 執行自動儲存
     */
    performAutoSave() {
        const $form = $("form[data-auto-save]");
        if ($form.length > 0) {
            const formData = $form.serialize();

            // 儲存到 localStorage
            const autoSaveKey = "autosave_" + window.location.pathname;
            localStorage.setItem(autoSaveKey, formData);

            this.showNotification("info", "自動儲存", "表單資料已自動儲存", {
                duration: 2000,
            });
        }
    }

    /**
     * 設置表單變更追蹤
     */
    setupFormChangeTracking() {
        // 記錄表單初始狀態
        $("form[data-track-changes]").each(function () {
            const $form = $(this);
            $form.data("initial-state", $form.serialize());
        });

        // 監聽表單變更
        $(document).on(
            "input change",
            "form[data-track-changes] input, form[data-track-changes] textarea, form[data-track-changes] select",
            (e) => {
                const $form = $(e.target).closest("form");
                const initialState = $form.data("initial-state");
                const currentState = $form.serialize();

                if (initialState !== currentState) {
                    $form.addClass("form-changed");
                    this.showUnsavedChangesWarning(true);
                } else {
                    $form.removeClass("form-changed");
                    this.showUnsavedChangesWarning(false);
                }
            }
        );

        // 頁面離開警告
        $(window).on("beforeunload", (e) => {
            if ($(".form-changed").length > 0) {
                const message = "您有未儲存的變更，確定要離開嗎？";
                e.returnValue = message;
                return message;
            }
        });
    }

    /**
     * 顯示未儲存變更警告
     */
    showUnsavedChangesWarning(show) {
        if (show) {
            if ($("#unsaved-changes-warning").length === 0) {
                const warningHtml = `
                    <div id="unsaved-changes-warning" class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <strong>注意：</strong> 您有未儲存的變更
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                $("form[data-track-changes]").prepend(warningHtml);
            }
        } else {
            $("#unsaved-changes-warning").remove();
        }
    }

    /**
     * 設置智能焦點管理
     */
    setupSmartFocus() {
        // 自動焦點到第一個輸入欄位
        $(document).ready(() => {
            const $firstInput = $(
                "form input:visible, form select:visible, form textarea:visible"
            ).first();
            if ($firstInput.length > 0) {
                setTimeout(() => {
                    $firstInput.focus();
                }, 100);
            }
        });

        // Enter 鍵智能導航
        $(document).on("keydown", "input, select", (e) => {
            if (e.keyCode === 13 && !e.shiftKey) {
                // Enter 鍵
                const $current = $(e.target);
                const $form = $current.closest("form");
                const $inputs = $form.find(
                    "input:visible, select:visible, textarea:visible"
                );
                const currentIndex = $inputs.index($current);

                if (currentIndex < $inputs.length - 1) {
                    e.preventDefault();
                    $inputs.eq(currentIndex + 1).focus();
                }
            }
        });
    }

    /**
     * 設置進度指示器
     */
    setupProgressIndicators() {
        // 表單完成度指示器
        this.setupFormCompletionIndicator();

        // 步驟進度指示器
        this.setupStepProgressIndicator();
    }

    /**
     * 設置表單完成度指示器 - 已禁用，因為邏輯有問題
     */
    setupFormCompletionIndicator() {
        // 暫時禁用虛假的進度條
        // 需要重新設計邏輯，考慮角色指派狀態
        console.log("表單進度指示器已禁用 - 需要重新設計");
    }

    /**
     * 更新表單進度
     */
    updateFormProgress($form) {
        const $requiredFields = $form.find("[required]");
        const $filledFields = $requiredFields.filter(function () {
            return $(this).val() && $(this).val().trim() !== "";
        });

        const progress = ($filledFields.length / $requiredFields.length) * 100;

        // 更新進度條
        let $progressBar = $form.find(".form-progress-bar");
        if ($progressBar.length === 0) {
            const progressHtml = `
                <div class="form-progress mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">表單完成度</small>
                        <small class="text-muted form-progress-text">0%</small>
                    </div>
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar form-progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            `;
            $form.prepend(progressHtml);
            $progressBar = $form.find(".form-progress-bar");
        }

        $progressBar.css("width", progress + "%");
        $form.find(".form-progress-text").text(Math.round(progress) + "%");

        // 更新進度條顏色
        $progressBar.removeClass("bg-danger bg-warning bg-success");
        if (progress < 30) {
            $progressBar.addClass("bg-danger");
        } else if (progress < 70) {
            $progressBar.addClass("bg-warning");
        } else {
            $progressBar.addClass("bg-success");
        }
    }

    /**
     * 設置步驟進度指示器
     */
    setupStepProgressIndicator() {
        // 為多步驟表單添加進度指示器
        $("form[data-step-progress]").each(function () {
            const $form = $(this);
            const steps = $form.data("step-progress") || 1;

            if (steps > 1) {
                // 創建步驟進度條
                const stepProgressHtml = `
                    <div class="step-progress mb-4">
                        <div class="step-progress-bar">
                            ${Array.from(
                                { length: steps },
                                (_, i) => `
                                <div class="step-item ${
                                    i === 0 ? "active" : ""
                                }" data-step="${i + 1}">
                                    <div class="step-number">${i + 1}</div>
                                    <div class="step-label">步驟 ${i + 1}</div>
                                </div>
                            `
                            ).join("")}
                        </div>
                    </div>
                `;

                $form.prepend(stepProgressHtml);
            }
        });
    }

    /**
     * 綁定事件
     */
    bindEvents() {
        // 通知關閉事件
        $(document).on("click", ".notification-close", (e) => {
            const notificationId = $(e.target)
                .closest(".notification-close")
                .data("notification-id");
            this.removeNotification(notificationId);
        });

        // 載入狀態重置事件
        $(document).on("ajaxComplete", () => {
            // 重置所有載入狀態
            $(".btn-loading").each((index, button) => {
                this.setButtonLoading($(button), false);
            });
        });
    }

    /**
     * 便捷方法：顯示成功通知
     */
    showSuccess(title, message, options = {}) {
        return this.showNotification("success", title, message, options);
    }

    /**
     * 便捷方法：顯示錯誤通知
     */
    showError(title, message, options = {}) {
        return this.showNotification("error", title, message, options);
    }

    /**
     * 便捷方法：顯示警告通知
     */
    showWarning(title, message, options = {}) {
        return this.showNotification("warning", title, message, options);
    }

    /**
     * 便捷方法：顯示資訊通知
     */
    showInfo(title, message, options = {}) {
        return this.showNotification("info", title, message, options);
    }
}

// 全域實例
window.centerUX = new CenterUXEnhancements();

// jQuery 插件
$.fn.enhanceUX = function (options = {}) {
    return this.each(function () {
        const $element = $(this);

        // 添加載入狀態支援
        if (options.loading !== false) {
            $element.attr("data-loading-button", "true");
        }

        // 添加表單追蹤
        if ($element.is("form") && options.trackChanges !== false) {
            $element.attr("data-track-changes", "true");
        }

        // 添加進度指示器
        if ($element.is("form") && options.showProgress !== false) {
            $element.attr("data-show-progress", "true");
        }

        // 添加自動儲存
        if ($element.is("form") && options.autoSave === true) {
            $element.attr("data-auto-save", "true");
        }
    });
};

// 使用範例：
// $('#centerForm').enhanceUX({
//     loading: true,
//     trackChanges: true,
//     showProgress: true,
//     autoSave: false
// });
