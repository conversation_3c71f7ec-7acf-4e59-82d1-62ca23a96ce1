# 多供應商同時結帳 - 方案 2 實施 TODO 清單

## 📋 專案概述

-   **目標**: 實現多供應商商品同時結帳功能
-   **方案**: 新增 `orderform_distributors` 關聯表
-   **預估工作量**: 5-8 工作天
-   **風險等級**: 🟢 低風險

---

## 🚀 第一階段：資料庫結構建立 (預估 1 天)

### ✅ 1.1 建立資料庫遷移檔案

-   [x] 建立遷移檔案 `create_orderform_distributors_table.php`
-   [x] 定義表結構：
    ```sql
    - id (主鍵)
    - orderform_id (訂單ID，外鍵)
    - distributor_id (供應商ID，外鍵)
    - created_at (建立時間)
    - 唯一索引 (orderform_id, distributor_id)
    ```
-   [x] 執行遷移測試

### ✅ 1.2 建立模型和關聯

-   [x] 建立 `app/Models/Main/Orderform.php` 模型
-   [x] 建立 `app/Models/Main/OrderformDistributor.php` 模型
-   [x] 定義 Orderform 與 Distributor 的多對多關聯
-   [x] 測試模型關聯是否正常

### ✅ 1.3 建立歷史資料遷移工具 ⭐ **重要**

-   [x] 建立 Artisan 命令 `php artisan migrate:orderform-distributors`
-   [x] 實作資料遷移邏輯：
    -   [x] 從 `orderform.distributor_id` 建立基礎關聯 (包含 0 = 平台訂單)
    -   [x] 從 `orderform_product.distributor_id` 收集額外供應商資料
    -   [x] 合併去重，確保每個 (orderform_id, distributor_id) 組合唯一
    -   [x] 批量插入避免效能問題
-   [x] 建立資料驗證邏輯
-   [x] 建立回滾機制
-   [x] 在測試環境執行並驗證結果

---

## 🛒 第二階段：購物車邏輯調整 (預估 2-3 天)

### ✅ 2.1 移除供應商限制檢查

**檔案**: `app/Http/Controllers/home/<USER>

-   [x] 移除 `do_choose_shop()` 方法中的供應商統一性檢查 (第 140-145 行)
-   [x] 確認移除註解的檢查邏輯不會影響其他功能
-   [x] 測試購物車可以加入不同供應商商品

### ✅ 2.2 調整訂單建立邏輯

**檔案**: `app/Http/Controllers/home/<USER>

-   [x] 修改 `buy()` 方法中的 `distributor_id` 設定邏輯 (第 590-594 行)
-   [x] 將多供應商訂單的 `orderform.distributor_id` 設為 0
-   [x] 在訂單建立後，新增關聯表記錄邏輯

### ✅ 2.3 更新 OrderHelper 建立訂單邏輯

**檔案**: `app/Services/pattern/OrderHelper.php`

-   [x] 修改 `createOrder()` 方法
-   [x] 在插入 `orderform_product` 後，收集所有供應商 ID
-   [x] 批量插入 `orderform_distributors` 關聯記錄
-   [x] 確保事務完整性

---

## ✅ 第三階段：查詢邏輯調整 (已完成)

### ✅ 3.1 調整訂單查詢核心邏輯

**檔案**: `app/Services/pattern/OrderHelper.php`

-   [x] 修改 `get_orders()` 方法 (第 1426-1433 行)
-   [x] 供應商篩選時改為 JOIN `orderform_distributors` 表
-   [x] 保持原有查詢邏輯的其他部分不變
-   [x] 測試供應商後台能正確查看包含自己商品的訂單

### ✅ 3.2 調整供應商訂單管理

**檔案**: `app/Http/Controllers/order/Supplier.php`

-   [x] 檢查 `get_product_data()` 方法是否需要調整
-   [x] 確認供應商回饋查詢邏輯正常運作
-   [x] 測試供應商回饋功能

### ✅ 3.3 調整權限檢查邏輯

**檔案**: `app/Http/Controllers/admin/MainController.php`

-   [x] 檢查 `check_controll()` 方法是否需要調整
-   [x] 確認供應商權限檢查邏輯
-   [x] 測試供應商只能操作自己相關的訂單

### ✅ 3.4 調整統計報表邏輯

**檔案**: `app/Http/Controllers/order/OrderCtrl.php`

-   [x] 修改 `group_excel()` 方法支援多供應商訂單統計
-   [x] 多供應商訂單金額平均分配給各供應商
-   [x] 保持向後兼容性

### ✅ 3.5 調整客服系統邏輯

**檔案**: `app/Http/Controllers/home/<USER>

-   [x] 修改訂單詢問的供應商判斷邏輯
-   [x] 單一供應商：使用該供應商
-   [x] 多供應商：預設使用平台
-   [x] 保持向後兼容性

---

## ✅ 第四階段：統計和報表調整 (已完成)

### 🐛 緊急修正：結帳錯誤修復 (已完成)

**問題**: 結帳時出現 `SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'is_registrable' cannot be null` 錯誤

**修正檔案**: `app/Services/pattern/OrderHelper.php`

-   [x] 修正 `createOrder()` 方法中的商品建立邏輯
-   [x] 修正 `importOrder()` 方法中的商品建立邏輯
-   [x] 為不能為 null 的欄位設定預設值
-   [x] 測試結帳功能正常運作

**修正內容**:

```php
// 定義不能為 null 的欄位及其預設值
$defaultValues = [
    'is_registrable' => 0,
    'deal_position' => 0,
    'pre_buy' => 0,
    'pre_buy_num' => 0,
    'product_cate' => 0,
    'bonus_model_id' => 0,
    'use_ad' => 0,
    'distributor_id' => 0,
    'vip_type_reward' => 0,
    'vip_type_require' => 0,
    'deduct_invest' => 0,
    'deduct_consumption' => 0,
    'price_cv' => 0.00000000,
    'price_supplier' => 0.00000000,
    'supplier_bonus' => 1,
    'do_award_supplier_time' => ''
];
```

**測試結果**:

-   ✅ 訂單 ID: 1063 成功建立
-   ✅ 多供應商商品正確處理 (供應商 4, 1613, 0)
-   ✅ 供應商關聯正確建立 (3 個關聯記錄)
-   ✅ 付款流程正常跳轉

---

## ✅ 第四階段：統計和報表調整 (已完成)

### ✅ 4.1 訂單分組統計

**檔案**: `app/Http/Controllers/order/OrderCtrl.php`

-   [x] 檢查 `group_excel()` 方法 (第 172-191 行)
-   [x] 確認多供應商訂單的統計邏輯
-   [x] 測試訂單分組匯出功能

### ✅ 4.2 供應商相關統計

-   [x] 檢查所有涉及供應商統計的報表
-   [x] 確認數據準確性
-   [x] 測試各種統計功能

### ✅ 4.3 統計功能驗證

**檔案**: `app/Console/Commands/TestMultiVendorStatistics.php`

-   [x] 建立統計功能測試命令
-   [x] 測試 Repository 統計方法
-   [x] 測試訂單分組統計邏輯
-   [x] 測試供應商回饋統計
-   [x] 驗證多供應商統計數據正確性

**測試結果**:

-   總關聯數: 2070 筆
-   平台訂單數: 1015 筆
-   供應商訂單數: 1055 筆
-   多供應商訂單數: 1010 筆 (99.5%)
-   供應商數量: 20 個
-   統計邏輯正確運作 ✅

---

## 🧪 第五階段：測試驗證 (預估 1-2 天)

⚠️ **注意**: 由於 ServiceProvider 中有資料庫查詢，Laravel 單元測試會失敗，採用以下替代測試方案：

### ✅ 5.1 手動功能測試 (主要測試方式)

-   [ ] **購物車測試**

    -   [ ] 加入不同供應商商品到購物車
    -   [ ] 確認可以正常結帳
    -   [ ] 測試各種商品組合
    -   [ ] 記錄測試結果到測試文檔

-   [ ] **訂單管理測試**

    -   [ ] 供應商後台查看訂單
    -   [ ] 管理員查看多供應商訂單
    -   [ ] 訂單狀態變更測試
    -   [ ] 截圖記錄測試過程

-   [ ] **分潤系統測試**
    -   [ ] 確認供應商回饋正常計算
    -   [ ] 測試分潤分配邏輯
    -   [ ] 驗證回饋時間標記
    -   [ ] 比對資料庫記錄

### ✅ 5.2 資料庫直接驗證測試

-   [ ] 編寫 SQL 腳本驗證資料完整性
-   [ ] 建立測試資料驗證腳本
-   [ ] 使用 Artisan 命令進行資料驗證
-   [ ] 建立測試報告模板

### ✅ 5.3 瀏覽器自動化測試 (可選)

-   [ ] 使用 Playwright/Selenium 進行 E2E 測試
-   [ ] 模擬真實用戶操作流程
-   [ ] 自動截圖和結果記錄

### ✅ 5.4 權限測試 (手動驗證)

-   [ ] 供應商 A 只能看到包含自己商品的訂單
-   [ ] 供應商 A 不能操作其他供應商的商品
-   [ ] 管理員可以查看所有訂單
-   [ ] 建立權限測試檢查表

---

## 🔧 第六階段：優化和收尾 (預估 1 天)

### ✅ 6.1 效能優化

-   [ ] 檢查查詢效能
-   [ ] 新增必要的資料庫索引
-   [ ] 優化關聯查詢

### ✅ 6.2 文檔更新

-   [ ] 更新技術文檔
-   [ ] 記錄變更內容
-   [ ] 建立回滾計畫

### ✅ 6.3 部署準備

-   [ ] 準備生產環境遷移腳本
-   [ ] 建立資料備份計畫
-   [ ] 準備監控和日誌

---

## ⚠️ 風險控制和注意事項

### 🔴 高風險項目

-   [ ] **資料庫遷移**: 確保在生產環境執行前充分測試
-   [ ] **歷史資料遷移**: 確保所有舊訂單資料正確遷移到關聯表
-   [ ] **事務完整性**: 確保訂單建立和關聯表插入在同一事務中
-   [ ] **權限邏輯**: 仔細測試供應商權限不會洩漏
-   [ ] **資料一致性驗證**: 遷移後驗證資料完整性

### 🟡 中風險項目

-   [ ] **查詢效能**: 監控新增 JOIN 對查詢效能的影響
-   [ ] **資料一致性**: 確保舊訂單和新訂單的處理邏輯一致
-   [ ] **大量資料遷移**: 注意遷移過程中的記憶體和時間消耗

### 🟢 低風險項目

-   [ ] **前端顯示**: 基本不需要調整
-   [ ] **分潤邏輯**: 已基於商品層級，無需調整

---

## 📝 測試檢查清單

### 基本功能測試

-   [ ] 單一供應商商品結帳 (確保不破壞現有功能)
-   [ ] 多供應商商品結帳 (新功能)
-   [ ] 供應商後台查看訂單
-   [ ] 管理員後台查看訂單
-   [ ] 訂單狀態變更
-   [ ] 供應商回饋處理

### 歷史資料遷移測試 ⭐ **新增**

-   [ ] 遷移前後資料數量比對
-   [ ] 單一供應商訂單遷移驗證
-   [ ] 多供應商訂單遷移驗證 (基於 orderform_product)
-   [ ] 平台訂單遷移驗證 (distributor_id = 0 的訂單)
-   [ ] 遷移效能測試 (大量資料)
-   [ ] 回滾機制測試

### 邊界情況測試

-   [ ] 空購物車處理
-   [ ] 單一商品多數量
-   [ ] 大量不同供應商商品
-   [ ] 網路異常情況處理
-   [ ] 歷史訂單和新訂單混合查詢

### 效能測試

-   [ ] 大量訂單查詢效能
-   [ ] 複雜條件篩選效能
-   [ ] 資料庫連線壓力測試
-   [ ] 關聯表 JOIN 查詢效能

---

## 🎯 成功標準

### 功能標準

-   ✅ 可以將不同供應商商品加入購物車並成功結帳
-   ✅ 供應商可以查看包含自己商品的所有訂單
-   ✅ 分潤系統正常運作
-   ✅ 所有現有功能保持正常

### 效能標準

-   ✅ 查詢效能不明顯下降 (< 10% 效能影響)
-   ✅ 資料庫儲存空間增加合理 (< 5% 增加)

### 安全標準

-   ✅ 供應商權限隔離正常
-   ✅ 資料完整性約束有效
-   ✅ 無資料洩漏風險

---

## 📞 聯絡和支援

如在實施過程中遇到問題，請記錄：

1. 具體錯誤訊息
2. 重現步驟
3. 預期行為 vs 實際行為
4. 相關程式碼片段

---

## 🔧 附錄：歷史資料遷移工具實作指南

### 資料遷移邏輯設計

#### 遷移策略

```php
// 1. 處理所有 orderform.distributor_id (包含 0 = 平台訂單)
// 2. 從 orderform_product.distributor_id 收集額外供應商
// 3. 合併去重，確保 (orderform_id, distributor_id) 唯一性
// 4. 批量插入避免效能問題
// 5. 提供進度顯示和錯誤處理
```

#### 資料驗證邏輯

```sql
-- 驗證遷移完整性的 SQL
SELECT
    COUNT(*) as total_orders,
    COUNT(DISTINCT od.orderform_id) as migrated_orders,
    COUNT(*) - COUNT(DISTINCT od.orderform_id) as missing_orders
FROM orderform o
LEFT JOIN orderform_distributors od ON o.id = od.orderform_id;

-- 驗證平台訂單 (distributor_id = 0) 是否正確遷移
SELECT COUNT(*) as platform_orders_migrated
FROM orderform_distributors
WHERE distributor_id = 0;
```

#### 回滾機制

```php
// 提供清理關聯表的回滾命令
// php artisan migrate:orderform-distributors --rollback
```

### 建議的 Artisan 命令結構

```bash
php artisan migrate:orderform-distributors [options]
  --dry-run          # 僅顯示將要遷移的資料，不實際執行
  --batch-size=1000  # 批量處理大小
  --rollback         # 回滾遷移
  --verify           # 驗證遷移結果
```

---

## 🧪 附錄：測試替代方案詳細指南

### 問題說明

由於 ServiceProvider 中有資料庫查詢，Laravel 的 PHPUnit 測試會在啟動時失敗，因此需要採用替代測試方案。

### 推薦測試方案

#### 1. 手動測試 + 文檔記錄 (主要方案)

```bash
# 建立測試記錄文檔
touch 多供應商功能測試記錄.md

# 測試流程：
# 1. 準備測試資料
# 2. 執行功能操作
# 3. 記錄結果和截圖
# 4. 驗證資料庫狀態
```

#### 2. SQL 腳本驗證 (資料完整性)

```sql
-- 建立驗證腳本 verify_multi_vendor.sql
-- 檢查關聯表資料
-- 驗證權限邏輯
-- 確認分潤計算
```

#### 3. Artisan 命令測試工具

```php
// 建立專用的測試命令
php artisan make:command TestMultiVendorCheckout
// 在命令中執行各種驗證邏輯
// 輸出測試結果報告
```

#### 4. 瀏覽器自動化 (進階選項)

```javascript
// 使用 Playwright 或 Puppeteer
// 模擬真實用戶操作
// 自動截圖和結果驗證
```

### 測試檢查清單模板

```markdown
## 多供應商結帳功能測試報告

### 測試環境

-   日期：
-   測試人員：
-   資料庫版本：

### 測試結果

-   [ ] 購物車多供應商商品添加
-   [ ] 結帳流程完整性
-   [ ] 訂單資料正確性
-   [ ] 供應商權限隔離
-   [ ] 分潤計算準確性

### 發現問題

1. 問題描述
2. 重現步驟
3. 預期結果 vs 實際結果
4. 解決方案
```

**預祝實施順利！** 🚀
