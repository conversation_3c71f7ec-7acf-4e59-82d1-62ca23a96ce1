<!DOCTYPE html>
<html lang="zh-Hans-TW" ng-app="app">

<head>
    <meta name="_token" content="{{ csrf_token() }}">
    <!-- ///////////////////////////////////////////////////////////////////////////// -->
    <!-- ///////////////////////////////////////////////////////////////////////////// -->
    <!-- ///////////////////////////////////////////////////////////////////////////// -->
    {!! $data['seo'][0]['verification'] !!}
    {!! $data['seo'][0]['trackgoogle'] !!}
    {!! $data['seo'][0]['marketgoogle'] !!}
    {!! $data['seo'][0]['marketyahoo'] !!}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2, user-scalable=0">
    <!-- SEO -->
    <title>@yield('title', $data['seo'][0]['title'])</title>
    <meta name="Keywords" CONTENT="@yield('Keywords', $data['seo'][0]['seokey'])">
    <meta name="description" lang="zh-Hant-TW" content="@yield('description', $data['seo'][0]['descr'])">
    <meta property="og:site_name" name="application-name" content="{{$data['seo'][0]['fb_name']}}" />
    <meta property="og:type" content="website" />
    <meta property="og:title" itemprop="name" content="@yield('ogtitle', $data['seo'][0]['fb_title'])">
    <meta property="og:description" itemprop="description" lang="zh-Hant-TW" content="@yield('ogdescription', $data['seo'][0]['fb_descr'])">
    <meta property="og:url" itemprop="url" content="@yield('ogurl', 'http://'.request()->server('HTTP_HOST').'/')">
    <meta property="og:image" content="@yield('ogimage', 'https://'.request()->server('HTTP_HOST').'/'.__PUBLIC__.$data['seo'][0]['fb_img'])">
    <meta property="og:locale" content="zh_TW">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="{{$data['seo'][0]['twitter_name']}}">
    <meta name="twitter:description" content="{{$data['seo'][0]['twitter_descr']}}">
    <meta name="twitter:domain" content="{{$data['seo'][0]['twitter_title']}}">

    <meta name="google-signin-client_id" content="config('extra.social_media.Google_appId')">

    <!--favicon-->
    <link rel="shortcut icon" href="{{__PUBLIC__}}/{{$data['admin_info']['favicon']}}" />
    <link rel="bookmark" href="{{__PUBLIC__}}/{{$data['admin_info']['favicon']}}" />

    <!-- PWA 加入桌面-->
    <link rel="manifest" href="/public/manifest.json">

    <link rel="stylesheet" href="{{__PUBLIC__}}/css/reset.css"> <!-- 初始化樣式 -->

    <!-- jquery -->
    <script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
    @if (config('extra.shop.google_recaptcha_sitekey'))
    <!-- 機器人驗證 --><!-- 放在jquery-ui引入前，避免衝突 -->
    <script type="text/javascript" src="https://www.google.com/recaptcha/api.js"></script>
    @endif

    <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

    <!-- bootstrap -->
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.0/umd/popper.min.js"></script> <!--輔助bootstrap rwd-->
    <script src="//stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.js"></script>
    <link rel="stylesheet" href="{{__PUBLIC__}}/css/bootstrap-icons.css">
    <link rel="stylesheet" href="{{__PUBLIC__}}/css/iconstyle.css">
    <!-- <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.0/js/bootstrap.min.js"></script> -->

    <!-- 引入套件 -->
    <!-- 輪播 -->
    <link rel="stylesheet" type="text/css" href="{{__PUBLIC__}}/css/owl.carousel.min.css">
    <link rel="stylesheet" type="text/css" href="{{__PUBLIC__}}/css/owl.theme.default.min.css">
    <script type="text/javascript" src="{{__PUBLIC__}}/js/owl.carousel.min.js"></script>

    <script src="//cdnjs.cloudflare.com/ajax/libs/modernizr/2.8.3/modernizr.js"></script> <!--判斷寬度-->
    <script src="//cdnjs.cloudflare.com/ajax/libs/shave/2.1.3/shave.min.js"></script> <!--截斷文字-->

    <!-- 驗證碼 -->
    <link rel="stylesheet" type="text/css" href="/public/static/jq-plugin/verify/verify.css">
    <script type="text/javascript" src="/public/static/jq-plugin/verify/verify.js"></script>

    <!-- ui元件 -->
    <link rel='stylesheet' href='//cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.4.1/semantic.min.css'>
    <script src="//cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.4.1/semantic.min.js"></script>

    <!-- iconfont -->
    <!-- <script src="https://kit.fontawesome.com/9a2047a60f.js" crossorigin="anonymous"></script> -->

    <!-- 字體 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@100;300;400;500;700;900&display=swap"
        rel="stylesheet">
    <!-- <link href="//fonts.googleapis.com/css?family=Open+Sans:300,400,600,700,800&display=swap" rel="stylesheet">-->

    <!-- 自訂樣式 -->
    <link rel="stylesheet" href="{{__PUBLIC__}}/css/style.css?{{rand(1000, 9999)}}">
    <link rel="stylesheet" href="{{__PUBLIC__}}/css/style2021.css?{{rand(1000, 9999)}}">
    <link rel="stylesheet" href="{{__PUBLIC__}}/css/body_block.css?{{rand(1000, 9999)}}">
    <link rel="stylesheet" href="{{__PUBLIC__}}/css/style_skychakra.css?{{rand(1000, 9999)}}">
    <!-- ///////////////////////////////////////////////////////////////////////////// -->
    @yield("css")

    @yield("mycode")

    @if ($data['close_nav'])
    <style>
        .content_area {
            padding-top: 0px;
        }

        #itemBox {
            justify-content: center;
        }

        #vue_desk_menu,
        .content_area .directoryRow,
        .announcement-fluid,
        #leftBox,
        .announcementRow .announcementBox,
        .popularProBranch {
            display: none;
        }

        .content_area {
            margin: 0;
        }

        #rightContentBox {
            max-width: 1140px;
        }
    </style>
    @endif
    <style>
        .navList li:hover {
            cursor: pointer;
        }

        img.icon_pic {
            width: 25px;
        }

        .icon-star:before {
            content: "\e90f";
        }

        .activeBtn {
            background-color: #ff7300;
            color: #fff;
        }

        .verification>div.cerify-code-panel {
            display: flex;
            align-items: center;
            flex-direction: row-reverse;
        }

        .verification div.verify-code-area {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    </style>
</head>

<body>
    <div id="body_block" class="body_block position-fixed w-100 h-100 bg-dark">
        <div class="loader">
            <span></span>
        </div>
    </div>
    <a id="registrationModel_btn" data-toggle="modal" data-target="#registrationModel" class="d-none"></a>
    <div id="phoneMenu" class="page">
        <!-- ///////////////////////////////////////////////////////////////////// -->
        <div id="vue_phone_menu">
            <!-- 手機主選單 ////////////////////////////////////////////////////// -->
            <nav class="panel mainPanel">
                <div class="phone_menu_head">
                    <div class="phone_menu_head_title">
                        <a href="/"><img src="{{__PUBLIC__}}{{$data['index_excel'][0]['data1']}}" alt="" class="w100"></a>
                    </div>
                    <div class="phone_menu_right">
                        <div class="closePanel" @click="clearLayerRecord()">
                            @include('home.Public.svg_cancel')
                        </div>
                    </div>
                </div>
                <ul style="display:flex;flex-direction:column;align-items:center;gap:12px;padding:0;list-style:none;">
                    @foreach($data['navigation_menu'] as $menu)
                    <li>
                        <a href="{{ $menu['menu_url'] }}" target="{{ $menu['target'] }}">
                            {{ $menu['menu_name'] }}
                        </a>
                    </li>
                    @endforeach
                </ul>
                <ul class="s-btn">
                    @include('home.Public.nav_addition_btn')
                </ul>
            </nav>

            <!-- 商品選單 ////////////////////////////////////////////////////// -->
            <nav class="panel proInfoPanel">
                <div class="d-flex justify-content-between align-items-center pl-3 pr-3">
                    <div class="closeSubPanel fs-3" @click="clearLayerRecord()">
                        @include('home.Public.svg_cancel')
                    </div>
                    <div class="cursor-pointer fs-3" @click="productMenuBack()"><i class="bi bi-arrow-left"></i>
                    </div>
                </div>
                <ul class="phoneMenu">
                    <li class="heading title mb-3">{{$data['frontend_menu']['product']['name']}}</li>
                    <li v-for="item in productMenu" class="item">
                        <a :href="'/index/product/' + item.action + '?id='+ item.id"
                            :class="['subheading', item.subType.length > 0?'arrow':'']">
                            <span>
                                <img class="icon_pic mr-2" :src="'{{__PUBLIC__}}/' + item.pic" v-if="item.pic">
                                <span v-text="item.title"></span>
                            </span>
                        </a>
                        <ul>
                            <li v-for="prop in item.subType">
                                <a v-if="prop.subType.length==0"
                                    :href="'/index/product/' + prop.action + '?id='+ prop.id">
                                    <span>
                                        <img class="icon_pic mr-2" :src="'{{__PUBLIC__}}/' + prop.pic" v-if="prop.pic">
                                        <span v-text="prop.title"></span>
                                    </span>
                                </a>
                                <a v-if="prop.subType.length > 0" class="pr-0"
                                    @click="clickProductMenu(prop.action, prop.id)">
                                    <span>
                                        <img class="icon_pic mr-2" :src="'{{__PUBLIC__}}/' + prop.pic" v-if="prop.pic">
                                        <span v-text="prop.title"></span>
                                    </span>
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
            <div class="nav_block"></div>
        </div>
        <!-- ///////////////////////////////////////////////////////////////////// -->
        <!-- <div class="wrapper" style="z-index: 98;"> -->
        <div class="wrapper">
            <div class="container-fluid limit-container nav_area">
                <!-- main content start -->
                <header id="vue_desk_menu">
                    <div class="header container position-relative">
                        <div class="logoImgBox">
                            <a href="{{$data['index_excel'][0]['data2']}}">
                                <div class="logoImg" style="background-image: url({{__PUBLIC__}}{{$data['index_excel'][0]['data1']}});"></div>
                            </a>
                        </div>
                        <div class="position-absolute" style="right:0;">
                            <div class="memberBox w-100">
                                <ul class="topBox m-0">
                                    <li>
                                        <div class="seachBox">
                                            <form action="{{url('Product/search')}}" method="get" name="searchForm"
                                                class="search-wrapper">
                                                @csrf
                                                <input type="text" name="keyword" placeholder="{{Lang::get('商品搜尋')}}">
                                                <button type="submit" onclick="searchForm.submit();">
                                                    @include('home.Public.svg_search')
                                                </button>
                                            </form>
                                        </div>
                                    </li>

                                    @include('home.Public.nav_addition_btn')
                                </ul>
                            </div>
                            <div class="menuTrigger">
                                <ul class="phoneCart">
                                    @if(empty(config('control.close_function_current')['會員管理']))
                                    @if($data['user']['id'] != 0)
                                    <!-- 會員已登入 -->
                                    <li>
                                        <a href="{{url('Member/member')}}">
                                            <i class="bi bi-person"></i>
                                        </a>
                                    </li>
                                    @else
                                    <!-- 會員未登入 -->
                                    <li>
                                        <a id="login" data-toggle="modal" data-target="#memberLogin"
                                            onclick="$('#bonus_info').hide()">
                                            <i class="bi bi-person"></i>
                                        </a>
                                    </li>
                                    @endif
                                    @endif
                                    <li>
                                        <a id="" data-toggle="modal" data-target="#phoneSearch">
                                            @include('home.Public.svg_search')
                                        </a>
                                    </li>
                                    @if(empty(config('control.close_function_current')['訂單管理']))
                                    @if($data['user']['id'] != 0 || empty(config('control.close_function_current')['會員管理'])==false)
                                    <li>
                                        <a href="{{url('Cart/choose_shop')}}">
                                            <i class="icon-shopping_cart"></i>
                                            <span class="counter prodNum mainbtn">
                                                {{($data['cartCount']) ?? "0"}}
                                            </span>
                                        </a>
                                    </li>
                                    @else
                                    <li>
                                        <a id="go_cart" class="checkoutBtn" href="###"
                                            data-toggle="modal" data-target="#memberLogin" onclick="$('#bonus_info').show()">
                                            <i class="icon-shopping_cart"></i>
                                            <span class="counter prodNum mainbtn">
                                                <span id="cartCount">{{($data['user']['cart_count']) ?? "0"}}</span>
                                            </span>
                                        </a>
                                    </li>
                                    @endif
                                    @endif
                                </ul>
                                <!-- burger-menu start -->
                                <div class="burger-menu">
                                    <div class="burger"></div>
                                </div>
                                <!-- burger-menu end -->
                            </div>
                        </div>
                    </div>
                    <nav id="desktopNav" class="desktopNav">
                        <div class="navBtnBox">
                            <div class="container">
                                <a id="left"><i class="bi bi-chevron-left"></i></a>
                                <a id="right"><i class="bi bi-chevron-right"></i></a>
                            </div>
                        </div>
                        <div class="container container-nav" id="desktopDownMenu">
                            <div class="scroll viewport">
                                <ul class="menu-main tab-nav" id="scroll">
                                    @if($data['index_online']['product_nav_total'] == 1)
                                    <li class="proInforLink top_nav">
                                        <a href="javascript:vue_phone_menuVM.clickProductMenu('product', '')">{{$data['frontend_menu']['product']['name']}}</a>
                                    </li>
                                    @endif
                                    @foreach($data['navigation_menu'] as $menu)
                                    <li class="proInforLink top_nav">
                                        <a href="{{ $menu['menu_url'] }}" target="{{ $menu['target'] }}">
                                            {{ $menu['menu_name'] }}
                                        </a>
                                    </li>
                                    @endforeach
                                    <!-- 也許以後會突然想打開 -->
                                    {{-- @if(empty(config('control.close_function_current')['有感體驗']) || empty(config('control.close_function_current')['折扣優惠']))
                                            <!-- 優惠專區 -->
                                            <li class="offerZoneLink top_nav">
                                                <a href="{{url('Product/activity')}}">{{$data['frontend_menu']['product']['second_menu']['activity']['name']}}</a>
                                    </li>
                                    @endif
                                    @if(empty(config('control.close_function_current')['有感體驗']))
                                    <!-- 有感體驗 -->
                                    <li class="top_nav"><a href="{{url('Experience/experience')}}">{{$data['frontend_menu']['experience']['name']}}</a></li>
                                    @endif
                                    @if(empty(config('control.close_function_current')['活動專區']))
                                    <!-- 活動專區 -->
                                    <li class="top_nav"><a href="{{url('Activity/activity')}}">{{$data['frontend_menu']['activity']['name']}}</a></li>
                                    @endif
                                    @if(empty(config('control.close_function_current')['會員文章分享']))
                                    <!-- 會員文章分享 -->
                                    <li class="top_nav"><a href="{{url('ShareArticle/index')}}">{{$data['frontend_menu']['share_article']['name']}}</a></li>
                                    @endif

                                    @if($data['index_online']['nav_other'] == 1)
                                    @include('home.Public.other_nav')
                                    @endif --}}
                                </ul>
                            </div>
                        </div>
                    </nav>
                </header>
            </div>

            <!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
            <div class="content_area">
                @yield('content')
            </div>
            <!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
            @if($data['index_online']['block11'] == 1)
            <footer>
                @if($data['index_online']['nav_other_footer'] == 1)
                <div class="footer-link">
                    <div class="container">
                        <ul>
                            @include('home.Public.other_nav')
                        </ul>
                    </div>
                </div>
                @endif
                <div class="container copy">
                    <div class="row">
                        <div class="col-lg-4 col-12 mb-3">
                            <h2 class="title">{{$data['index_excel'][27]['data2']}}</h2>
                            {!! html_entity_decode($data['index_excel'][27]['data3']) !!}
                        </div>
                        <div class="col-lg-5 col-12 mb-3">
                            <h2 class="title">{{$data['index_excel'][28]['data2']}}</h2>
                            {!! html_entity_decode($data['index_excel'][28]['data3']) !!}
                        </div>
                        <div class="col-lg-3 col-12 mb-3">
                            <h2 class="title">{{$data['index_excel'][29]['data2']}}</h2>
                            {!! html_entity_decode($data['index_excel'][29]['data3']) !!}

                            <h2 class="title">{{$data['index_excel'][30]['data2']}}</h2>
                            {!! html_entity_decode($data['index_excel'][30]['data3']) !!}
                        </div>

                        <div class="col-12 copyrightBox">
                            <p>Copyright &#169; {{date('Y')}} {{$data['company_name']}} ,Ltd. All rights reserved &nbsp;&nbsp;
                                <a class="" href="{{url('Login/privacy_rule')}}" target="_blank">{{Lang::get('隱私政策')}}</a>
                                <span id="addToHome_btn" data-toggle="modal" data-target="#addToHome" style="visibility: hidden;">+</span>
                            </p>
                            <div class="photonic-footer" style="opacity: 0.3;">
                                <span>{{Lang::get('傳訊光科技')}}:</span>
                                <a target="_blank" class="text-photonic" href="https://shop.photonic.com.tw/">{{Lang::get('購物車')}}</a>/
                                <a target="_blank" class="text-photonic" href="https://www.photonic.com.tw/">{{Lang::get('網頁設計')}}</a>/
                                <a target="_blank" class="text-photonic" href="http://erp2000.com/">{{Lang::get('CRM')}}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            @endif
            <!-- main content end -->
            <!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->

            <!-- social-bar start -->
            <div class="social-bar">
                <input type="checkbox" id="menu-link" name="">
                <div class="check-menu social-arrow">
                    @if($data['index_excel'][2]['data2'] || $data['index_excel'][3]['data2'] || $data['index_excel'][4]['data2']
                    || $data['index_excel'][5]['data2'] || $data['index_excel'][38]['data2'])
                    <label for="menu-link" class="hb">
                        <i class="bi bi-chevron-left"></i>
                    </label>
                    @endif
                </div>

                <ul class="fixed-menu" for="menu-link">
                    <li>
                        @if($data['index_excel'][2]['data2'])
                        <a href="{{$data['index_excel'][2]['data2']}}" target="_blank">
                            <div class="social-bar__item social-bar__line">
                                <!-- <i class="icon-line" aria-hidden="true"></i> -->
                                <img src="{{__PUBLIC__}}/img/icon/icon-line.png">
                            </div>
                        </a>
                        @endif
                    </li>
                    <li>
                        @if($data['index_excel'][3]['data2'])
                        <a href="{{$data['index_excel'][3]['data2']}}" target="_blank">
                            <div class="social-bar__item social-bar__fb">
                                <!-- <i class="icon-facebook" aria-hidden="true"></i> -->
                                <img src="{{__PUBLIC__}}/img/icon/icon-fb.png">
                            </div>
                        </a>
                        @endif
                    </li>
                    <li>
                        @if($data['index_excel'][41]['data2'])
                        <a href="{{$data['index_excel'][41]['data2']}}" target="_blank">
                            <div class="social-bar__item social-bar__youtube">
                                <!-- <i class="bi bi-youtube"></i> -->
                                <img src="{{__PUBLIC__}}/img/icon/icon-youtube.png">
                            </div>
                        </a>
                        @endif
                    </li>
                    <li>
                        @if($data['index_excel'][39]['data2'])
                        <a href="{{$data['index_excel'][39]['data2']}}" target="_blank">
                            <div class="social-bar__item social-bar__tiktok">
                                <!-- <i class="bi bi-tiktok"></i> -->
                                <img src="{{__PUBLIC__}}/img/icon/icon-tiktok.png">
                            </div>
                        </a>
                        @endif
                    </li>
                    <!-- <li>
                        @if($data['index_excel'][40]['data2'])
                        <a href="{{$data['index_excel'][40]['data2']}}" target="_blank">
                            <div class="social-bar__item social-bar__wechat">
                                <img src="{{__PUBLIC__}}/img/icon/icon-wechat.png">
                            </div>
                        </a>
                        @endif
                    </li> -->
                    <li>
                        @if($data['index_excel'][38]['data2'])
                        <a href="{{$data['index_excel'][38]['data2']}}" target="_blank">
                            <div class="social-bar__item social-bar__instagram">
                                <i class="bi bi-instagram"></i>
                            </div>
                        </a>
                        @endif
                    </li>
                    <li>
                        @if($data['index_excel'][4]['data2'])
                        <a href="mailto:{{$data['index_excel'][4]['data2']}}">
                            <div class="social-bar__item social-bar__mailbox">
                                <i class="icon-letter" aria-hidden="true"></i>
                            </div>
                        </a>
                        @endif
                    </li>
                    <li>
                        @if($data['index_excel'][5]['data2'])
                        <a href="tel:{{$data['index_excel'][5]['data2']}}">
                            <div class="social-bar__item social-bar__phone">
                                <i class="icon-phone" aria-hidden="true"></i>
                            </div>
                        </a>
                        @endif
                    </li>
                </ul>
            </div>
            <!-- social-bar end -->

            <!-- ad Side start -->
            @if($data['index_online']['block13'] == 1)
            <div class="adSidebg @if(isset($data['closeAdSide'])) d-none @endif">
                <div class="adSideBox">
                    <a class="closeAdSide">
                        @include('home.Public.svg_cancel')
                    </a>
                    <a href="{{$data['index_excel'][6]['data2']}}">
                        <img class="" src="{{__PUBLIC__}}{{$data['index_excel'][6]['data1']}}" alt="">
                    </a>
                </div>
            </div>
            @endif
            <!-- ad Side end -->

            <a href="#" class="goTop"><i class="bi bi-chevron-up"></i> </a>
        </div>
    </div>

    <!-- 跳出視窗們 -->
    @yield('Modal')
    <!-- 跳出視窗：商品搜尋 -->
    <div class="modal fade phoneSearch smallMOdel" id="phoneSearch" tabindex="-1" role="dialog" aria-labelledby="memberLoginTitle" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="memberLoginTitle">{{Lang::get('商品搜尋')}}</h5>
                </div>
                <div class="modal-body">
                    <div class="phoneSearchBox">
                        <form action="{{url('Product/search')}}" method="get" name="searchForm" class="search-wrapper">
                            @csrf
                            <input type="text" name="keyword" placeholder="{{Lang::get('商品搜尋')}}">
                            <button type="submit" onclick="searchForm.submit();">
                                @include('home.Public.svg_search')
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 跳出視窗：會員登入 -->
    <div class="modal fade shoppingCart memberLogin smallMOdel" id="memberLogin" tabindex="-1" role="dialog" aria-labelledby="memberLoginTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="memberLoginTitle">{{Lang::get('會員登入')}}</h5>
                </div>
                <div class="modal-body">
                    <form action="{{url('Login/pagelogin')}}" method="post" name="newloginForm">
                        @csrf
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <div class="form-group">
                            <div class="col-md-2 col-12">
                                <div class="row">
                                    <label>{{Lang::get('帳號')}}({{Lang::get('手機')}})</label>
                                </div>
                            </div>
                            <div class="col-md-10 col-12">
                                <div class="row">
                                    <input type="text" class="form-control" name="account" placeholder="{{Lang::get('手機')}}" value="" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-md-2 col-12">
                                <div class="row"><label>{{Lang::get('密碼')}}</label></div>
                            </div>
                            <div class="col-md-10 col-12">
                                <div class="row"><input type="password" name="password" class="form-control" placeholder="Password" value="" /></div>
                            </div>
                        </div>
                        <div class="verifyCode">
                            <label>{{Lang::get('驗證碼')}}</label>
                            <div class="verification" id="verification_newloginForm"></div>
                        </div>
                        <div class="submitBox">
                            <input class="submitBtn cursor-pointer border-0" type="button" value="{{Lang::get('登入')}}" onclick="submitForm('newloginForm')" id="check_btn_newloginForm">
                            <ul class="registeredBox">
                                <li>
                                    <a href="{{url('Login/signup')}}" class="d-inline-block registereBtn">
                                        {{Lang::get('立即註冊')}}
                                    </a>
                                </li>
                                |
                                <li>
                                    <a class="d-inline-block forgetPwdBtn" data-dismiss="modal" aria-label="Close" id="forgetPassword1">
                                        {{Lang::get('忘記密碼')}}
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </form>
                    <div class="row justify-content-center thPartyPlatformBox">
                        @if(config('extra.social_media.FB_appID'))
                        <a href="###" onclick="FBLogin(location.href);" class="p-0 col-sm-4 col-12">
                            <img class="p-1" src="{{__PUBLIC__}}/img/icon_facebook-en.png" alt="">
                        </a>
                        @endif
                        @if(config('extra.social_media.client_id'))
                        <a href="###" onclick="LineAuth(location.href);" class="p-0 col-sm-4 col-12">
                            <img class="p-1" src="{{__PUBLIC__}}/img/icon_line-en.png" alt="">
                        </a>
                        @endif
                        @if(config('extra.social_media.Google_appId'))
                        <a href="###" onclick="GoogleLogin(0, location.href);" class="p-0 col-sm-4 col-12">
                            <img class="p-1" src="{{__PUBLIC__}}/img/icon_google.png" alt="">
                        </a>
                        @endif
                    </div>

                    <div id="bonus_info" class="registeredBox" style="display: none;">
                        <!-- <p class="tourist">
                            {{Lang::get('輕鬆快速的享受購物')}}
                            <a href="{{url('Cart/choose_shop')}}">{{Lang::get('以遊客身分結帳')}}</a>
                        </p> -->
                        <p>{{Lang::get('您可加入會員，獲得更多好康優惠')}}：</p>
                        <ol>
                            <li>1.{{Lang::get('享有會員專屬優惠券折扣')}}</li>
                            <li>2.{{Lang::get('紅利點數累積折抵消費')}}</li>
                            <li>3.{{Lang::get('不定時優惠資訊推播')}}</li>
                            <li>
                                <span class="immediately">{{Lang::get('立刻加入領好康')}}</span>
                                <a href="{{url('Login/signup')}}" class="d-inline-block registereBtn">
                                    {{Lang::get('立即註冊')}}
                                </a>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 跳出視窗：網紅登入 -->
    <div class="modal fade shoppingCart memberLogin smallMOdel" id="kolLogin" tabindex="-1" role="dialog"
        aria-labelledby="kolLoginTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="memberLoginTitle">{{Lang::get('網紅登入')}}</h5>
                </div>
                <div class="modal-body">
                    <form action="{{url('Kol/pagelogin')}}" method="post" name="kolloginForm">
                        @csrf
                        <div class="form-group">
                            <div class="col-md-2 col-12">
                                <div class="row">
                                    <label>{{Lang::get('帳號')}}</label>
                                </div>
                            </div>
                            <div class="col-md-10 col-12">
                                <div class="row">
                                    <input type="text" class="form-control" name="email" aria-describedby="emailHelp" placeholder="Email" value="" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-md-2 col-12">
                                <div class="row">
                                    <label>{{Lang::get('密碼')}}</label>
                                </div>
                            </div>
                            <div class="col-md-10 col-12">
                                <div class="row">
                                    <input type="password" name="password" class="form-control" placeholder="Password" value="" />
                                </div>
                            </div>
                        </div>
                        <div class="verifyCode">
                            <label>{{Lang::get('驗證碼')}}</label>
                            <div class="verification" id="verification_kolloginForm"></div>
                        </div>
                        <div class="submitBox">
                            <input class="submitBtn cursor-pointer border-0" type="button" value="{{Lang::get('登入')}}" onclick="submitForm('kolloginForm')" id="check_btn_kolloginForm">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 跳出視窗：註冊商品 -->
    <div class="modal fade shoppingCart smallMOdel registrationModel" id="registrationModel" tabindex="-1" role="dialog" aria-labelledby="registrationModelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="registrationModelTitle">{{Lang::get('商品註冊')}}</h5>
                </div>
                <div class="modal-body">
                    <div class="row ">
                        <form action="{{url('Product/reg')}}" method="post" name="newloginForm3" enctype="multipart/form-data" class="w-100">
                            @csrf
                            <input type="hidden" name="distributor_id" value="0">
                            <div class="form-group col-12">
                                <label for="in_data">{{Lang::get('購買日期')}}</label>
                                <input type="date" name="buytime" id="in_data" class="form-control" placeholder="{{Lang::get('購買日期')}}" required>
                            </div>
                            <div class="form-group col-12">
                                <label for="in_tax_ID_number" class="col-form-label">{{Lang::get('發票號碼')}}</label>
                                <input type="text" name="tax_ID_number" class="form-control" id="in_tax_ID_number" placeholder="{{Lang::get('發票號碼')}}" value="" required>
                            </div>
                            <div class="form-group col-12">
                                <label for="in_product_code">{{Lang::get('機身號碼(公司貨方可註冊)')}}</label>
                                <input type="text" name="product_code" class="form-contro form-control" id="in_product_code" placeholder="{{Lang::get('機身號碼')}}" value="" required>
                            </div>
                            <div class="form-group col-12">
                                <label class="col-form-label">{{Lang::get('產品名稱')}}</label>
                                <select name="product_name" id="in_product" class="ui fluid selection dropdown no label">
                                    <option value="">{{Lang::get('請選擇產品名稱')}}</option>
                                </select>
                            </div>
                            <div class="form-group col-12">
                                <label for="in_img">{{Lang::get('上傳發票照片')}}</label>
                                <div class="image-box">
                                    <input type='file' ref="img" id="in_img" class="upl" name="image" accept="image/*">
                                    <img class="preview" name="image" />
                                </div>
                            </div>
                            <div class="form-group col-12 text-center justify-content-center">
                                <a class="submitBtn" onclick="reg_check()">{{Lang::get('送出')}}</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 跳出視窗：忘記密碼跳 -->
    <a style="display: none;" id="goForgetPasswordModel" data-toggle="modal" data-target="#forgetPasswordModel"></a>
    <div class="modal fade shoppingCart smallMOdel forgetPasswordModel" id="forgetPasswordModel" tabindex="-1" role="dialog"
        aria-labelledby="forgetPasswordModelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="forgetPasswordModelTitle">{{Lang::get('忘記密碼')}}</h5>
                </div>
                <div class="modal-body">
                    <form class="hid" id="newforgotForm2" action="{{url('Login/forgot_form')}}" method="post">
                        @csrf
                        <div class="form-group">
                            <div class="col-md-2 col-12">
                                <div class="row">
                                    <label><span class="text-danger">*</span>{{Lang::get('帳號')}}({{Lang::get('手機')}})</label>
                                </div>
                            </div>
                            <div class="col-md-10 col-12">
                                <div class="row">
                                    <input type="text" name="account_forget" class="form-control" placeholder="{{Lang::get('將寄送修改密碼信')}}" required>
                                </div>
                            </div>
                            <div class="col-md-2 col-12" style="margin-top: 40px;">
                                <div class="row">
                                    <label><span class="text-danger">*</span>{{Lang::get('信箱email')}}</label>
                                </div>
                            </div>
                            <div class="col-md-10 col-12" style="margin-top: 40px;">
                                <div class="row">
                                    <input type="email" name="email_forget" class="form-control" placeholder="{{Lang::get('將寄送修改密碼信')}}" required>
                                </div>
                            </div>
                            <div class="w-100 d-flex justify-content-center mt-3 alert">
                                <p style="margin-top: -25px;">請填寫您的會員信箱；若無，則另請提供信箱。</p>
                            </div>

                            @if(config('extra.shop.google_recaptcha_sitekey'))
                            <div class="w-100 d-flex justify-content-center mt-3">
                                <div class="g-recaptcha text-center" data-callback="captcha_onclick"
                                    data-sitekey="{{config('extra.shop.google_recaptcha_sitekey')}}">
                                </div>
                                <input type="hidden" name="recaptcha" id="recaptchaValidator_forget" />
                            </div>
                            @endif
                        </div>
                        <div class="submitBox">
                            <label class="submitBtn">{{Lang::get('送出')}}<input name="subject" type="submit" onclick="validateForgotForm()">
                            </label>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 跳出視窗：加入主畫面 -->
    <div class="modal fade shoppingCart" id="addToHome" tabindex="-1" role="dialog" aria-labelledby="addToHomeTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="addToHomeTitle">{{Lang::get('加入主畫面')}}</h5>

                </div>
                <div class="modal-body">
                    {{Lang::get('在手機主畫面建立捷徑，快速享受購物樂趣~')}}<br>
                    <button id="addToHomeBtn" class="btn btn-success">{{Lang::get('建立')}}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 跳出視窗：加入購物車功能 -->
    <div id="priceOption">
        <!-- 跳出視窗：選擇品項 -->
        <span id="selectTypeBtn" data-toggle="modal" data-target="#selectType"></span>
        <div class="modal fade smallMOdel" id="selectType" tabindex="-1" role="dialog" aria-labelledby="selectTypeTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <div class="modal-header">
                        <h5 class="modal-title" id="selectTypeTitle">{{Lang::get('選擇規格')}}</h5>
                    </div>
                    <div class="modal-body">
                        <div id="buyBlockShow" class="productIntroBox" v-if="productinfo.has_price!=0">
                            <div class="w-100">
                                <!-- 下拉選式 -->

                                <!-- <div class="d-flex selectBox w-100">
                                    <span class="selectTitle">{{Lang::get('商品規格')}}</span>
                                    <select class="form-control" id="priceSelect" v-model="priceSelect">
                                        <option v-for="pricevo in price"
                                            :value="pricevo.id"
                                            v-text="pricevo.title">
                                        </option>
                                    </select>
                                </div>
                                <div class="activityBox">
                                    <p class="mark">↑{{Lang::get('其他方案請點選這裡')}}↑</p>
                                </div> -->

                                <div class="activityBox w-100 mb-2">
                                    <span class="activity">
                                        {{Lang::get('商品規格')}}：
                                        <span v-if="selectTypes.length>1" v-text="get_selectTypes_name(' ')"></span>
                                    </span>
                                </div>
                                <!-- 直接點選式 -->
                                <div class="format-items  activityBox position-absolute invisible">
                                    <span v-for="pricevo in price">
                                        <label class="price_option"
                                            :for="'priceOption2_' + pricevo.id"
                                            :data="pricevo.title" v-text="pricevo.title"
                                            :pic_index="pricevo.pic_index"
                                            @click="changePic(pricevo.pic_index)"></label>
                                        <input type="radio" name="priceOption2" class="radio_price_option" radios="price_option"
                                            v-model="priceSelect"
                                            :id="'priceOption2_' + pricevo.id" :value="pricevo.id">
                                    </span>
                                </div>
                                <!-- 多品項組合式 -->
                                <div v-for="(types, index) in show_type" class="format-items  activityBox">
                                    <span v-for="type in types">
                                        <label :for="'type2_' + index + '_' + type" v-text="type" :class="'type_' + index" @click="changeTypes()"></label>
                                        <input type="radio" class="radio_price_option"
                                            :name="'type2_' + index"
                                            :radios="'type_' + index"
                                            v-model="selectTypes[index]"
                                            :id="'type2_' + index + '_' + type" :value="type">
                                    </span>
                                </div>
                            </div>

                            <div class="amountBox priceBox">
                                <span class="offerPrice">
                                    {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}
                                    <span class="price" v-if="current_price.count">
                                        <span v-text="current_price.count.toLocaleString('en-US')"></span>
                                    </span>
                                </span>
                                <template v-if="current_price.price">
                                    <span class="originalPrice" v-if="current_price.price != current_price.count">
                                        {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}
                                        <span class="lineThrough" v-if="current_price.price">
                                            <span v-text="current_price.price.toLocaleString('en-US')"></span>
                                        </span>
                                    </span>
                                </template>
                            </div>
                            <div class="amount-item">
                                <div class="spinnerBox">
                                    <p>{{Lang::get('數量')}}</p>
                                    <div class="input-group spinner">
                                        <div class="input-group-prepend">
                                            <button class="btn text-monospace" type="button" @click="changeNum(-1)">-</button>
                                        </div>
                                        <input type="number" class="count form-control" min="1" :max="limit_num" step="1" v-model="itemcounter">
                                        <div class="input-group-append">
                                            <button class="btn text-monospace" type="button" @click="changeNum(1)">+</button>
                                        </div>
                                    </div>
                                </div>
                                @if(empty(config('control.close_function_current')['庫存警示']))
                                <div class="specialZone">
                                    <div class="activityBox">
                                        <span class="activity">
                                            <span v-for="pricevo in price" :class="[pricevo.id != current_price.id ? 'd-none' : '']"
                                                v-text="'{{Lang::get('庫存數')}}：' + pricevo.num">
                                            </span>
                                        </span>
                                    </div>
                                </div>
                                @endif
                                <div class="bankStagingBox">
                                    @if(isset($data['card_pay']))
                                    @if($data['card_pay'] == 1)
                                    <div v-if="productinfo.card_pay == 1">
                                        <p><span class="">{{Lang::get('可以刷卡')}}</span></p>
                                    </div>
                                    <div v-if="productinfo.card_pay != 1">
                                        <p><span class="num">{{Lang::get('不可以刷卡')}}</span></p>
                                    </div>
                                    @else
                                    <p><span class="num">{{Lang::get('不可以刷卡')}}</span></p>
                                    @endif
                                    @endif
                                </div>
                            </div>
                            <div class="row no-gutters bottomBox">
                                <div class="col-12 mt-2">
                                    <a href="javascript:void(0)" class="submitBtn" onclick="type_selected()">{{Lang::get('確認')}}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 跳出視窗：前往結帳 -->
        <span class="d-none" data-toggle="modal" data-target="#checkoutModal" id="get_checkout"></span>
        <div class="modal fade smallMOdel " id="checkoutModal" tabindex="-1" role="dialog" aria-labelledby="checkoutModalTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <div class="modal-header">
                        <h5 class="modal-title" id="checkoutModalTitle">{{Lang::get('加入購物車')}}</h5>
                    </div>
                    <div class="modal-body">
                        <div>
                            <p class="add">{{Lang::get('商品已放入購物車')}}</p>
                            <!-- <p class="shoppingCart">{{Lang::get('您的購物車商品總數')}}：<span class="numPro"></span></p> -->
                            <p>{{Lang::get('目前總金額')}}：
                                <span class="amount">{{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}<span id="totalAmount"></span></span>
                                ({{Lang::get('折扣前')}})
                            </p>
                        </div>
                        <br>
                        <div class="d-flex justify-content-between">
                            <a id="goCheckout" class="submitBtn col-6" onclick='checkout()' data-dismiss="modal" aria-label="Close">
                                {{Lang::get('前往結帳')}}
                            </a>
                            <a id="goCheckout" class="submitBtn2 col-6" data-dismiss="modal" aria-label="Close">
                                {{Lang::get('繼續購物')}}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@fingerprintjs/fingerprintjs@3/dist/fp.min.js"></script>
    <script>
        // 初始化FingerprintJS
        async function get_visitorId() {
            let fpPromise = await FingerprintJS.load();
            let result = await fpPromise.get();
            visitorId = result.visitorId;

            return visitorId;
        }
    </script>

    <!-- Vue js -->
    <script src="//cdnjs.cloudflare.com/ajax/libs/Vue.Draggable/2.14.1/vuedraggable.min.js"></script>
    <script type="text/javascript" src="/public/static/admin/js/vue.min.js"></script>
    <script src="https://unpkg.com/vue-toasted@1.1.28/dist/vue-toasted.min.js"></script>
    <script>
        Vue.use(Toasted);
        const vt_error_obj = {
            duration: 1500,
            className: ["toasted-primary", "bg-danger"]
        };
        const vt_success_obj = {
            duration: 1500,
            className: ["toasted-primary", "bg-success"]
        };
        const vt_warning_obj = {
            duration: 1500,
            className: ["toasted-primary", "bg-warning"]
        };
    </script>

    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script> <!--跳出訊息-->

    <script>
        var csrf_token = $('meta[name="_token"]').attr('content')

        /*手機產品資訊選單*/
        var data_vue_menu = {
            layer_record: [],
            productMenu: [],
            offerMenu: [],
            activityMenu: [],
            sideProductMenu: [],
        };
        var vue_phone_menuVM = new Vue({
            el: '#vue_phone_menu',
            data: data_vue_menu,
            methods: {
                /*取得產品選單*/
                getProductMenu: function(action, action_id, category_type) {
                    self = this;
                    var requestData = {
                        action: action,
                        id: action_id,
                    };

                    // 如果有指定 category_type，加入到請求資料中
                    if (category_type !== undefined && category_type !== null) {
                        requestData.category_type = category_type;
                    }

                    $.ajax({
                        type: "POST",
                        headers: {
                            'X-CSRF-Token': csrf_token
                        },
                        dataType: "json",
                        data: requestData,
                        url: "{{url('Ajax/productMenu')}}", //產品資訊
                        success: function(data) {
                            time = 0;
                            if ($('.proInfoPanel').hasClass('isOpen')) {
                                time = 500;
                                $('.proInfoPanel').removeClass('isOpen');
                            }

                            setTimeout(function() {
                                vue_phone_menuVM.productMenu = data;
                                console.log(data);
                                $('.proInfoPanel').addClass('isOpen');
                                $('.mainPanel').addClass('isOpen');
                                $("body").addClass("on-side");

                            }, time);
                        },
                        error: function(e) {
                            Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
                        }
                    });
                },

                /*點擊產品選單*/
                clickProductMenu: function(action, action_id, category_type) {
                    self = this;
                    /*紀錄點擊階層*/
                    self.layer_record.push({
                        action: action,
                        action_id: action_id,
                        category_type: category_type
                    });
                    self.getProductMenu(action, action_id, category_type);
                },

                /*回上一層*/
                productMenuBack: function() {
                    self = this;

                    if (self.layer_record.length >= 2) {
                        layer_record = self.layer_record[self.layer_record.length - 2];
                    } else {
                        layer_record = {
                            action: "",
                            action_id: "",
                            category_type: null
                        };
                    }

                    self.layer_record.pop();
                    action = layer_record.action;
                    action_id = layer_record.action_id;
                    category_type = layer_record.category_type;

                    if (action == '') {
                        $('.proInfoPanel').removeClass('isOpen');
                    } else {
                        self.getProductMenu(action, action_id, category_type);
                    }
                },

                /*清空階層紀錄*/
                clearLayerRecord: function() {
                    self = this;
                    self.layer_record = [];
                },

                /*優惠專區選單*/
                offerNavMenu: function() {
                    $.ajax({
                        method: "GET",
                        dataType: "json",
                        url: "{{url('Ajax/offerNavMenu')}}",
                        success: function(data) {
                            vue_phone_menuVM.offerMenu = data['offerMenu']; /*優惠專區選單*/
                            vue_phone_menuVM.activityMenu = data['activityMenu']; /*活動專區選單*/
                            // console.log(vue_phone_menuVM.offerMenu)
                            // console.log(vue_phone_menuVM.activityMenu)
                        }
                    });
                },

                /*左側商品選單*/
                getProdAsideMenu: function(category_type) {
                    const distributor_id = "{{request()->get('distributor_id') ?? 0}}";
                    var requestData = {
                        action: "{{$data['action']}}",
                        id: "{{request()->get('id') ?? ''}}",
                    };

                    // 如果有指定 category_type，加入到請求資料中
                    if (category_type !== undefined && category_type !== null) {
                        requestData.category_type = category_type;
                    }

                    $.ajax({
                        type: "POST",
                        headers: {
                            'X-CSRF-Token': csrf_token
                        },
                        // async: false,
                        dataType: "json",
                        url: "{{url('Ajax/getProdAsideMenu')}}?distributor_id=" + distributor_id, //取得左側商品選單
                        data: requestData,
                        success: function(data) {
                            vue_phone_menuVM.sideProductMenu = data;

                            setTimeout(() => {
                                if (requestData.action == 'typeinfo') {
                                    data.forEach((item) => {
                                        if (item.subType.length > 0) {
                                            item.subType.forEach((type) => {
                                                if (type.id == parseInt(requestData.id)) {
                                                    $(`span[data-target="#collapse${item.id}"]`).click();
                                                }
                                            });
                                        }
                                    });
                                }
                            }, 200);
                        },
                    });
                },
            },
        });
        // vue_phone_menuVM.offerNavMenu();
        vue_phone_menuVM.getProdAsideMenu();
        /*電腦選單(與手機選單共用資料)*/
        var vue_desk_menuVM = new Vue({
            el: '#vue_desk_menu',
            data: data_vue_menu,
            methods: {},
        });
        /*左側商品選單(與手機選單共用資料)*/
        var proAccordionVM = "";

        if ($('#proAccordion').length) {
            proAccordionVM = new Vue({
                el: '#proAccordion',
                data: data_vue_menu,
                methods: {
                    window_innerWidth: function() {
                        return window.innerWidth;
                    },
                },
            });
        }

        window.addEventListener('resize', function() {
            if (proAccordionVM) {
                proAccordionVM.$forceUpdate();
            }
        });
    </script>

    <script src="{{__PUBLIC__}}/js/adSide.js?20241113"></script>
    <script src="{{__PUBLIC__}}/js/goTop.js"></script>
    <!-- <script src="{{__PUBLIC__}}/js/search.js"></script> -->
    <script src="{{__PUBLIC__}}/js/scrollNavBar.js"></script>
    <script src="{{__PUBLIC__}}/js/mobileDevice.js"></script>
    <script src="{{__PUBLIC__}}/js/phoneSideMenu.js"></script>
    <!-- <script src="{{__PUBLIC__}}/js/dropdown.js"></script> -->
    <!-- <script src="{{__PUBLIC__}}/js/cartAdd.js"></script> -->
    <script src="{{__PUBLIC__}}/js/timeSelect.js"></script>
    <script>
        function unregistered() {
            Swal.fire({
                title: "{{Lang::get('請先登入會員')}}",
                icon: 'warning',
                content: '',
                confirmButtonText: "{{Lang::get('確認')}}",
                confirmButtonColor: 'var(--btn-mainlink)',
            }).then((result) => {
                $('#login').click();
            });
        }

        function register_product(distributor_id) {
            $('form[name="newloginForm3"] input[name="distributor_id"]').val(distributor_id);

            $.ajax({
                type: "post",
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                datatype: 'json',
                data: {
                    distributor_id: distributor_id
                },
                url: "{{url('Product/get_reg_prod')}}",
                success: function(result) {
                    let option_html = `<option value="">{{Lang::get('請選擇產品名稱')}}</option>`;
                    data = result.msg;

                    for (var i = data.length - 1; i >= 0; i--) {
                        option_html += (`<option value="${data[i].product_name}">${data[i].product_name}</option>`);
                    }

                    $('#in_product').html(option_html);
                    $('#registrationModel_btn').click();
                },
                error: function(xhr) {
                    Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
                    console.error(xhr);
                }
            });
        }

        // 監聽點擊忘記密碼
        $("#forgetPassword1").on("click", function() {
            $('#memberLogin').one('hidden.bs.modal', function(e) {
                $('#goForgetPasswordModel').click();
            })
        });

        // 驗證 for 忘記密碼 form
        function validateForgotForm() {
            const account = document.querySelector('input[name="account_forget"]').value.trim();
            const email = document.querySelector('input[name="email_forget"]').value.trim();

            if (!account) {
                Swal.fire({
                    title: "{{Lang::get('請輸入帳號')}}",
                    icon: 'warning',
                    confirmButtonText: "{{Lang::get('確認')}}",
                    confirmButtonColor: 'var(--btn-mainlink)',
                });

                return false;
            }

            if (!email) {
                Swal.fire({
                    title: "{{Lang::get('請輸入信箱')}}",
                    icon: 'warning',
                    confirmButtonText: "{{Lang::get('確認')}}",
                    confirmButtonColor: 'var(--btn-mainlink)',
                });

                return false;
            }

            // 驗證信箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (!emailRegex.test(email)) {
                Swal.fire({
                    title: "{{Lang::get('請輸入正確的信箱格式')}}",
                    icon: 'warning',
                    confirmButtonText: "{{Lang::get('確認')}}",
                    confirmButtonColor: 'var(--btn-mainlink)',
                });

                return false;
            }

            // 所有驗證通過，提交表單
            document.getElementById('newforgotForm2').submit();
        }

        @if(config('extra.shop.google_recaptcha_sitekey'))
        /*機器人驗證*/
        function captcha_forget_onclick() {
            document.getElementById('recaptchaValidator_forget').value = grecaptcha.getResponse();
        }

        window.addEventListener('pageshow', (event) => {
            if (grecaptcha.getResponse()) {
                grecaptcha.reset();
            }
        });
        @endif

        /*desk選單加active*/
        function urlActive() {
            // var htmlHref = window.location.href;
            var htmlHref = window.location.href.replace(window.location.search, '');
            htmlHref = htmlHref.replace(/^http:\/\/[^/]+/, "");
            var addr = htmlHref.substr(htmlHref.lastIndexOf('/', htmlHref.lastIndexOf('/') - 1) + 1);
            var index = addr.lastIndexOf("\/");
            var addrLast = decodeURI(addr.substring(index + 1, addr.length));
            var str = decodeURI(addr.substring(0, index));
            // console.log(addr);
            $("[href$='" + addr + "']").parent('li').addClass('active');

            if (addr == 'about/about_story' || addr == 'about/about_map' || addr == 'about/about_contact') {
                $('.aboutUsLink').addClass('active');
            }

            if (addr == 'product/typeinfo' || addr == 'product/product' || addr == 'product/productinfo') {
                $('.proInforLink').addClass('active');
            }

            if (addr == 'product/activity') {
                $('.offerZoneLink').addClass('active');
            }

            if (addr == 'activity/activity_c') {
                $("[href$='/index/activity/activity']").parent('li').addClass('active');
            }

            if (addr == 'distribution/distribution') {
                $("[href$='/index/distribution/distribution?id={$stronghold.id}']").parent('li.top_nav').addClass('active');
            }
        }
        urlActive();

        var Request = new Object();
        Request = GetRequest();

        function GetRequest() {
            var url = location.search.replaceAll('+', '');
            var theRequest = new Object();

            if (url.indexOf('?') != -1) {
                var str = url.substr(1);
                strs = str.split('&');

                for (var i = 0; i < strs.length; i++) {
                    theRequest[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1]);
                }
            }

            return theRequest;
        }
        var proId = Request['id'];
        var tagId = Request['tag'];
    </script>
    @if(!empty($data['product']['id']))
    <script>
        var tagId = "{{ $data['product']['id'] }}";
    </script>
    @endif

    <!-- //////////////////////////////////////// -->
    @yield('ownJS')
    <!-- //////////////////////////////////////// -->

    @if(config('extra.social_media.FB_appID'))
    <!-- FB登入 -->
    <script type="text/javascript">
        //應用程式編號，進入 //developers.facebook.com/apps/ 即可看到
        //FB Login 官方文件：//developers.facebook.com/docs/facebook-login/web
        // Load the Facebook Javascript SDK asynchronously
        (function(d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0];

            if (d.getElementById(id)) {
                return;
            }

            js = d.createElement(s);
            js.id = id;
            js.src = "//connect.facebook.net/en_US/sdk.js";
            fjs.parentNode.insertBefore(js, fjs);
        }(document, 'script', 'facebook-jssdk'));
        window.fbAsyncInit = function() {
            FB.init({
                appId: "{{config('extra.social_media.FB_appID')}}", //FB appID
                cookie: true, // enable cookies to allow the server to access the session
                xfbml: true, // parse social plugins on this page
                version: 'v3.0' // use graph api version
            });
        };
        //使用自己客製化的按鈕來登入
        function FBLogin(redirect = '') {
            localStorage.setItem('redirect', redirect);

            FB.login(function(response) {
                //debug用
                console.log(response);
                if (response.status === 'connected') {
                    //user已登入FB
                    //抓userID
                    let FB_ID = response['authResponse']['userID'];
                    console.log('userID:' + FB_ID);
                    $.ajax({
                        url: "{{url('Login/fb_login')}}",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-Token': '{{csrf_token()}}'
                        },
                        type: 'POST',
                        data: {
                            U3: FB_ID,
                            ig: FB_ID
                        },
                        contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                        success: function(result) {
                            if (result.code == 1) {
                                location.href = result.msg;
                            }
                        }
                    });
                } else {
                    // user FB取消授權
                    Vue.toasted.show("{{Lang::get('授權失敗')}}", vt_error_obj);
                }
            }, {
                scope: 'public_profile,email'
            });
        }

        function Del_FB_App() {
            FB.getLoginStatus(function(response) {
                console.log(response);
                if (response.status === 'connected') {
                    // Logged into Facebook.
                    // 抓userID
                    FB.api("/me/permissions", "DELETE", function(response) {
                        console.log("刪除結果");
                        console.log(response); // gives true on app delete success
                    });
                } else {
                    // FB取消授權
                    console.log("無法刪除FB App");
                }
            }, true);
            /*
                FB.getLoginStatus(function (response) { // 取得目前user是否登入FB網站
                    //debug用
                });
            */
        }
    </script>
    @endif
    @if(config('extra.social_media.client_id'))
    <!-- LINE登入 -->
    <script type="text/javascript">
        function LineAuth(redirect = '') {
            localStorage.setItem('redirect', redirect);

            var URL = '//access.line.me/oauth2/v2.1/authorize?';
            URL += 'response_type=code';
            URL += `&client_id={{config('extra.social_media.client_id ')}}`;
            URL += `&redirect_uri={{config('extra.social_media.line_url ')}}`;
            URL += '&state=00';
            URL += '&scope=openid%20profile';
            window.location.href = URL;
        }
    </script>
    @endif
    @if(config('extra.social_media.Google_appId'))
    <!-- Google登入 -->
    <script type="text/javascript">
        function GoogleLogin(open, redirect = '') {
            localStorage.setItem('redirect', redirect);

            // Google's OAuth 2.0 endpoint for requesting an access token
            var oauth2Endpoint = 'https://accounts.google.com/o/oauth2/v2/auth';

            // Create <form> element to submit parameters to OAuth 2.0 endpoint.
            var form = document.createElement('form');
            form.setAttribute('method', 'GET'); // Send as a GET request.
            form.setAttribute('action', oauth2Endpoint);

            // Parameters to pass to OAuth 2.0 endpoint.
            var params = {
                'client_id': "{{config('extra.social_media.Google_appId')}}",
                'redirect_uri': "{{url('Login/g_access_token')}}",
                'response_type': 'token',
                'scope': 'https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email',
                'include_granted_scopes': 'true',
                'state': JSON.stringify({
                    open: open,
                    redirect: redirect,
                }),
            };

            // Add form parameters as hidden input values.
            for (var p in params) {
                var input = document.createElement('input');
                input.setAttribute('type', 'hidden');
                input.setAttribute('name', p);
                input.setAttribute('value', params[p]);
                form.appendChild(input);
            }

            // Add form to page and submit it to open the OAuth 2.0 endpoint.
            document.body.appendChild(form);
            form.submit();
        }
    </script>
    @endif
    <!-- 註冊商品相關功能 -->
    <script>
        /*送出註冊產品表單*/
        function reg_check() {
            var error = 0;
            if ($('#in_img').val() == '') {
                alert("{{Lang::get('請上傳發票照片')}}");

                return;
            }

            if ($('#in_product').val() == '') {
                alert("{{Lang::get('請填寫產品名稱')}}");

                return;
            }

            if ($('#in_product_code').val() == '') {
                alert("{{Lang::get('請填寫機身號碼')}}");

                return;
            }

            if ($('#in_tax_ID_number').val() == '') {
                alert("{{Lang::get('請填寫發票號碼')}}");

                return;
            }

            if ($('#in_data').val() == '') {
                alert("{{Lang::get('請填寫購買日期')}}");

                return;
            }

            newloginForm3.submit();
        }
    </script>

    <!-- 領取優惠券 -->
    <script>
        function getCoupon(id) {
            if ("{{$data['user']['id']}}" != '' && "{{$data['user']['id']}}" != '0') {
                var login = true;
            } else {
                var login = false;
            }

            if (!login) {
                unregistered();
            } else {
                $.ajax({
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    async: true,
                    datatype: 'json',
                    data: {
                        coupon_id: id,
                        cmd: 'assign',
                        num: 1,
                    },
                    url: "{{url('Product/getFreeCoupon')}}",
                    success: function(result) {
                        alert(result.msg);
                    },
                    error: function(xhr) {
                        Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
                        console.error(xhr);
                    }
                })
            }
        }
    </script>

    <!-- 表單驗證碼 -->
    <script type="text/javascript">
        /*一般登入驗證碼*/
        var codeVerifyCheck_newloginForm = true
        $('#verification_newloginForm').codeVerify({
            type: 1,
            // arith:24,
            width: '100%',
            height: '40px',
            fontSize: '30px',
            codeLength: 4,
            btnId: 'check_btn_newloginForm',
            ready: function() {},
            success: function() {
                codeVerifyCheck_newloginForm = true;
            },
            error: function() {
                Vue.toasted.show("{{Lang::get('驗證失敗')}}", vt_error_obj);
                codeVerifyCheck_newloginForm = false;
            }
        });

        /*網紅登入驗證碼*/
        var codeVerifyCheck_kolloginForm = true
        $('#verification_kolloginForm').codeVerify({
            type: 1,
            // arith:24,
            width: '100%',
            height: '40px',
            fontSize: '30px',
            codeLength: 4,
            btnId: 'check_btn_kolloginForm',
            ready: function() {},
            success: function() {
                codeVerifyCheck_kolloginForm = true;
            },
            error: function() {
                Vue.toasted.show("{{Lang::get('驗證失敗')}}", vt_error_obj);
                codeVerifyCheck_kolloginForm = false;
            }
        });
        $('.verify-change-code').each(function(i, t) {
            $(t).html("{{Lang::get('換一張')}}");
        })

        /*送出表單前先驗證*/
        function submitForm(formName) {
            setTimeout(function() {
                if (eval('codeVerifyCheck_' + formName)) {
                    eval(formName).submit();
                }
            }, 50);
        }
    </script>

    <!-- 加入購物車相關功能 -->
    <script>
        var add_method = '';

        function type_selected() {
            eval(add_method + '()');
            $('#selectType').click();
        }

        function select_type(method) {
            // console.log(method);
            if (window.innerWidth <= 991 || location.href.indexOf('productinfo') == -1) {
                /*小螢幕時 或 不在商品商品詳細內容頁*/
                add_method = method;
                if (priceOptionVM.price.length > 1) {
                    $('#selectTypeBtn').click();

                    return;
                }
            }
            eval(method + '()');
        }

        /*加入購物車*/
        function cartCtrl() {
            // console.log(response)
            var successFunction = function(response) {
                ///////add btn animatione///////////////////////////
                change_cart_num(response.msg);
                ////////////////////////////////////////////////////

                $.ajax({
                    type: "GET",
                    async: true,
                    dataType: "json",
                    url: "{{url('Product/cart')}}",
                    success: function(msg) {
                        msg = msg ? msg.toLocaleString('en-US') : '';
                        $('#totalAmount').html(msg);
                        $('#get_checkout').click();
                    }
                });
            }
            ajax_cartCtrl(successFunction); /*送出請求*/
        }
        /*直接購物*/
        function cartCtrlGO() {
            var successFunction = function(response) {
                ///////add btn animatione///////////////////////////
                change_cart_num(response.msg);
                ////////////////////////////////////////////////////

                if ("{{$data['user']['id']}}" != '0' || "{{empty(config('control.close_function_current')['會員管理'])}}" == "") {
                    document.location.href = "{{url('Cart/choose_shop')}}";
                } else {
                    $('#go_cart').click(); //會員登入畫面
                }
            }
            ajax_cartCtrl(successFunction); /*送出請求*/
        }
        /*變更購物車圖示數量*/
        function change_cart_num(num) {
            const prodNum_select = '.counter.prodNum';
            let prodNums = $(prodNum_select);

            for (var i = 0; i < prodNums.length; i++) {
                $(prodNums[i]).html(num);
                $(prodNums[i]).addClass('pulse');
            }

            setTimeout(function() {
                $(prodNum_select).removeClass('pulse');
            }, 510);
        }
        /*發送加入購物車請求*/
        function ajax_cartCtrl(successFunction, product_id = '', num = 0) {
            if (!product_id) {
                if (priceOptionVM.current_price.id) {
                    product_id = priceOptionVM.current_price.id.toString();
                } else {
                    alert("{{Lang::get('請選擇品項')}}");

                    return;
                }
            }

            if (!num) {
                num = priceOptionVM.itemcounter.toString();

                if (!num) {
                    alert("{{Lang::get('請選擇數量')}}");
                }
            }

            theRequest = GetRequest();
            product_id = (theRequest.kol) ? product_id + '_kol' + theRequest.kol : product_id;

            $.ajax({
                url: "{{url('Cart/cartCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                datatype: 'json',
                data: {
                    cmd: 'increase',
                    num: num,
                    product_id: product_id
                },
                error: function(xhr) {
                    alert("{{Lang::get('操作失敗')}}");
                    console.error(xhr);
                },
                success: function(response) {
                    // console.log(response);
                    if (response.code) {
                        /*執行指定的成功後function*/
                        successFunction(response);

                        if (typeof(productifnAreaVM) != 'undefined') {
                            productifnAreaVM.itemcounter = 1;
                        }

                        if (typeof(priceOptionVM) != 'undefined') {
                            priceOptionVM.itemcounter = 1;
                        }
                    } else {
                        if (response.msg) {
                            alert(response.msg);
                        } else {
                            alert("{{Lang::get('操作失敗')}}");
                        }
                        // console.error(response.message);
                    }
                }
            });
        }

        /*前往結帳按鈕*/
        function checkout() {
            if ("{{$data['user']['id']}}" != '0' || "{{empty(config('control.close_function_current')['會員管理'])}}" == "") {
                document.location.href = "{{url('Cart/choose_shop')}}";
            } else {
                $('#go_cart').click(); //會員登入畫面
            }
        }

        /*初始化vue 品項選擇 跳出視窗區*/
        if (typeof(productinfoVMData) == 'undefined') {
            var productinfoVMData = {
                /* 品項功能 ----------------------------*/
                productinfo: {
                    has_price: '1',
                    card_pay: '1'
                },
                price_json: "[]",
                /*品項json*/
                priceSelect: 0,
                /*選擇的品項id*/
                itemcounter: 1,
                /*加入購物車的數量*/
                limit_num: 10,
                /*加入購物車數量上限*/
                selectTypes: [],
                /*選擇的品項分段*/
            };
        }
        var priceOptionVM = new Vue({
            el: '#priceOption',
            data: productinfoVMData,
            @include('home.product.select_type_vue')
        });
        setTimeout(function() {
            // priceOptionVM.priceSelect = priceOptionVM.price ? priceOptionVM.price[0]['id'] : 0;  /*初始化選擇的品項id*/
            priceOptionVM.resetTypes(); /*初始化選擇的品項分段*/
        }, 100);

        /*呼叫這個方法並傳入商品id，就可在列表將商品加入購物車*/
        // priceOptionVM.set_price_sets_and_add_cart(prod_id);
        /*呼叫這個方法並傳入商品id，就可在列表將商品加入收藏*/
        // priceOptionVM.set_store_product_list(status, prod_id);
    </script>
    <script>
        window.addEventListener('pageshow', (event) => {
            $('#body_block').hide();
        });
    </script>
</body>

</html>