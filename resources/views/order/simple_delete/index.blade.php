@extends('admin.layout')

@section('title', '簡化訂單刪除工具')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">簡化訂單刪除工具</h3>
                </div>
                <div class="card-body">
                    <!-- 方法1：根據訂單ID刪除 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>方法1：根據訂單ID刪除</h5>
                            <div class="form-group">
                                <label for="order_ids">訂單ID (多個ID用逗號分隔)</label>
                                <textarea class="form-control" id="order_ids" rows="3" 
                                    placeholder="例如: 1001, 1002, 1003"></textarea>
                                <small class="form-text text-muted">
                                    輸入要刪除的訂單ID，多個ID用逗號分隔
                                </small>
                            </div>
                            <button type="button" class="btn btn-danger" onclick="deleteByIds()">
                                根據ID刪除訂單
                            </button>
                        </div>
                    </div>

                    <hr>

                    <!-- 方法2：根據條件搜尋並刪除 -->
                    <div class="row">
                        <div class="col-12">
                            <h5>方法2：根據條件搜尋並刪除</h5>
                            <form id="searchForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="user_number">會員編號</label>
                                            <input type="text" class="form-control" id="user_number" name="user_number">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="transport_location_name">收件人姓名</label>
                                            <input type="text" class="form-control" id="transport_location_name" name="transport_location_name">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="transport_location_phone">收件人手機</label>
                                            <input type="text" class="form-control" id="transport_location_phone" name="transport_location_phone">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="transport_email">收件人Email</label>
                                            <input type="email" class="form-control" id="transport_email" name="transport_email">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="create_time_start">訂單開始日期</label>
                                            <input type="date" class="form-control" id="create_time_start" name="create_time_start">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="create_time_end">訂單結束日期</label>
                                            <input type="date" class="form-control" id="create_time_end" name="create_time_end">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="total_min">最小金額</label>
                                            <input type="number" class="form-control" id="total_min" name="total_min" step="0.01">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="total_max">最大金額</label>
                                            <input type="number" class="form-control" id="total_max" name="total_max" step="0.01">
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-warning" onclick="searchOrders()">
                                    搜尋符合條件的訂單
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- 搜尋結果預覽 -->
                    <div id="searchResults" style="display: none;" class="mt-4">
                        <h5>搜尋結果預覽</h5>
                        <div id="orderPreview" class="alert alert-info"></div>
                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                            確認刪除這些訂單
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="cancelDelete()">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 載入中遮罩 -->
<div id="loadingOverlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; text-align: center;">
        <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
        </div>
        <div class="mt-2">處理中...</div>
    </div>
</div>
@endsection

@section('ownJS')
<script>
let previewOrderIds = [];

function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'block';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

function showMessage(message, type = 'info') {
    alert(message); // 簡單的提示，可以替換為更好的UI組件
}

async function deleteByIds() {
    const orderIds = document.getElementById('order_ids').value.trim();
    
    if (!orderIds) {
        showMessage('請輸入訂單ID', 'warning');
        return;
    }

    if (!confirm('確定要刪除這些訂單嗎？此操作無法復原！')) {
        return;
    }

    showLoading();
    
    try {
        const response = await fetch('{{ url("/order/simple_delete/delete_by_ids") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                order_ids: orderIds
            })
        });

        const result = await response.json();
        
        if (result.success) {
            showMessage(result.message, 'success');
            document.getElementById('order_ids').value = '';
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        showMessage('請求失敗：' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

async function searchOrders() {
    const formData = new FormData(document.getElementById('searchForm'));
    const data = Object.fromEntries(formData);
    
    // 檢查是否至少有一個條件
    const hasCondition = Object.values(data).some(value => value.trim() !== '');
    if (!hasCondition) {
        showMessage('請至少輸入一個搜尋條件', 'warning');
        return;
    }

    showLoading();
    
    try {
        const response = await fetch('{{ url("/order/simple_delete/delete_by_conditions") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();
        
        if (result.success && result.preview) {
            // 顯示預覽結果
            previewOrderIds = result.order_ids;
            document.getElementById('orderPreview').innerHTML = 
                `<strong>${result.message}</strong><br><br>` +
                result.orders.join('<br>');
            document.getElementById('searchResults').style.display = 'block';
        } else {
            showMessage(result.message, result.success ? 'info' : 'error');
        }
    } catch (error) {
        showMessage('請求失敗：' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

async function confirmDelete() {
    if (!confirm('確定要刪除這些訂單嗎？此操作無法復原！')) {
        return;
    }

    showLoading();
    
    try {
        const response = await fetch('{{ url("/order/simple_delete/confirm_delete") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                order_ids: previewOrderIds
            })
        });

        const result = await response.json();
        
        if (result.success) {
            showMessage(result.message, 'success');
            cancelDelete();
            // 清空表單
            document.getElementById('searchForm').reset();
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        showMessage('請求失敗：' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

function cancelDelete() {
    document.getElementById('searchResults').style.display = 'none';
    previewOrderIds = [];
}
</script>
@endsection
