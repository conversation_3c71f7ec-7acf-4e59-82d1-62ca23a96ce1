{{-- 多人角色指派組件 --}}
<div class="multiple-role-container" data-role="{{ $role->code }}">
    {{-- 現有成員列表 --}}
    <div class="current-members mb-3">
        <h6 class="mb-2">
            現有成員
            <span class="badge badge-secondary members-count" style="display: none;"></span>
        </h6>
        <div class="members-list">
            {{-- 動態內容將由 JavaScript 或現有資料填充 --}}
        </div>
        <div class="no-members text-muted" style="display: none;">
            <small>尚未指派任何 {{ $role->name }}</small>
        </div>
    </div>

    {{-- 新增成員區域 --}}
    <div class="add-member-section">
        <div class="row">
            <div class="col-md-8">
                <div class="form-group mb-2">
                    <label class="form-label">新增 {{ $role->name }}</label>
                    <input type="text"
                           class="form-control account-autocomplete"
                           data-role="{{ $role->code }}"
                           data-singleton="0"
                           placeholder="輸入會員編號搜尋..."
                           autocomplete="off">
                    <small class="form-text text-muted">
                        輸入至少2個字元開始搜尋會員編號
                    </small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group mb-2">
                    <label class="form-label">分潤百分比 (%)</label>
                    <input type="number"
                           class="form-control profit-percentage-input"
                           data-role="{{ $role->code }}"
                           placeholder="0.0"
                           min="0"
                           max="100"
                           step="0.1">
                    <small class="form-text text-muted">
                        請輸入0-100之間的數值
                    </small>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between align-items-center">
            <button type="button" class="btn btn-sm btn-success assign-multiple-role">
                <i class="bi bi-person-plus"></i> 新增 {{ $role->name }}
            </button>
            <div class="percentage-summary">
                <small class="text-muted">
                    目前總計: <span class="current-total-percentage" data-role="{{ $role->code }}">0.0</span>%
                    <span class="percentage-warning text-danger" style="display: none;">
                        (超過100%)
                    </span>
                </small>
            </div>
        </div>
    </div>

    {{-- 載入現有資料 --}}
    @if(isset($center) && $center)
        @php
            $currentStaff = collect($center->activeStaff ?? [])->filter(function($staff) use ($role) {
                return $staff->role && $staff->role->code === $role->code;
            });
        @endphp

        @if($currentStaff->count() > 0)
            <script>
                $(document).ready(function() {
                    const $container = $('.multiple-role-container[data-role="{{ $role->code }}"]');
                    const $membersList = $container.find('.members-list');
                    const roleCode = '{{ $role->code }}';

                    // 添加現有成員
                    @foreach($currentStaff as $staff)
                        const memberHtml{{ $loop->index }} = `
                            <div class="member-item mb-2" data-account-id="{{ $staff->account_id }}" data-staff-id="{{ $staff->id }}">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <span>
                                            <strong>{{ $staff->account->number ?? '' }}</strong>
                                            {{ $staff->account->name ? ' - ' . $staff->account->name : '' }}
                                            <small class="text-muted d-block">
                                                ({{ $staff->start_at ? \Carbon\Carbon::parse($staff->start_at)->format('Y/m/d') : '' }} 起)
                                            </small>
                                        </span>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="input-group input-group-sm">
                                            <input type="number"
                                                   class="form-control profit-percentage-edit"
                                                   value="{{ $staff->profit_percentage ?? 0 }}"
                                                   data-staff-id="{{ $staff->id }}"
                                                   data-role="{{ $role->code }}"
                                                   min="0"
                                                   max="100"
                                                   step="0.1">
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-right">
                                        <button type="button" class="btn btn-sm btn-outline-primary update-profit-percentage"
                                                data-staff-id="{{ $staff->id }}" data-role="{{ $role->code }}">
                                            <i class="bi bi-check"></i> 更新
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-multiple-role"
                                                data-role="{{ $role->code }}" data-account-id="{{ $staff->account_id }}">
                                            <i class="bi bi-x"></i> 移除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                        $membersList.append(memberHtml{{ $loop->index }});
                    @endforeach

                    // 更新成員數量
                    updateMembersCount($container);

                    // 隱藏無成員提示
                    $container.find('.no-members').hide();

                    // 初始化總分潤百分比顯示
                    updateTotalPercentageDisplay('{{ $role->code }}');
                });
            </script>
        @else
            <script>
                $(document).ready(function() {
                    const $container = $('.multiple-role-container[data-role="{{ $role->code }}"]');
                    $container.find('.no-members').show();
                });
            </script>
        @endif
    @endif
</div>

<style>
.multiple-role-container {
    min-height: 100px;
}

.member-item {
    padding: 12px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.member-item strong {
    color: #495057;
}

.members-list:empty + .no-members {
    display: block !important;
}

.members-count {
    font-size: 0.75em;
}

.add-member-section {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
    margin-top: 15px;
}

.form-label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}

.profit-percentage-input, .profit-percentage-edit {
    text-align: right;
}

.percentage-summary {
    font-size: 0.9em;
}

.current-total-percentage {
    font-weight: bold;
    color: #28a745;
}

.percentage-warning {
    font-weight: bold;
}

.input-group-sm .form-control {
    font-size: 0.875rem;
}

.member-item .row {
    margin: 0;
}

.member-item .col-md-3, .member-item .col-md-6 {
    padding-left: 5px;
    padding-right: 5px;
}

@media (max-width: 768px) {
    .member-item .col-md-3, .member-item .col-md-6 {
        margin-bottom: 10px;
    }

    .member-item .text-right {
        text-align: left !important;
    }
}
</style>
