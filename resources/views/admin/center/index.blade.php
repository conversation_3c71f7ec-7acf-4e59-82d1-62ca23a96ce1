@extends('admin.Public.aside')
@section('title')中心管理@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('static/admin/css/center-performance.css') }}">
@endpush

@section('content')
    <div id="content" class="center-management">
        <ul id="title" class="brand-menu">
            <li><a href="###">中心管理</a></li>
            <li><a href="###" onclick="javascript:location.href='/order/center/index'">中心列表</a></li>
            @if(!empty($data['filters']['name']))
                <li>搜尋：{{ $data['filters']['name'] }}</li>
            @endif
        </ul>

        <!-- 搜尋和篩選區塊 -->
        <div class="searchbox">
            <form action="" method="get" class="searchKeyBox">
                <div class="row">
                    <div class="col-md-3">
                        <input type="text" name="name" class="form-control"
                               placeholder="搜尋中心名稱"
                               value="{{ $data['filters']['name'] ?? '' }}">
                    </div>
                    <div class="col-md-3">
                        <select name="center_level_id" class="form-control">
                            <option value="">選擇中心等級</option>
                            @foreach($data['center_levels'] as $level)
                                <option value="{{ $level->id }}"
                                        {{ ($data['filters']['center_level_id'] ?? '') == $level->id ? 'selected' : '' }}>
                                    {{ $level->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-control">
                            <option value="">選擇狀態</option>
                            <option value="1" {{ ($data['filters']['status'] ?? '') == '1' ? 'selected' : '' }}>啟用</option>
                            <option value="0" {{ ($data['filters']['status'] ?? '') == '0' ? 'selected' : '' }}>停用</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn sendbtn"
                                data-loading-button="true"
                                data-loading-text="搜尋中...">
                            <i class="bi bi-search"></i> 搜尋
                        </button>
                        <a href="/order/center/index" class="btn clearbtn">
                            <i class="bi bi-x-circle"></i> 清除
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- 操作按鈕區塊 -->
        <div class="frame">
            <a href="/order/center/create" class="btn clearbtn">
                <i class="bi bi-plus-lg add small"></i> 新增中心
            </a>
            <div class="d-inline-block position-relative">
                <div class="edit" onclick="Show('.edit-item')">批量操作 <span class="bi bi-chevron-down"></span></div>
                <div class="edit-item none">
                    <a onclick="multiEnable();">
                        <p class="mb-0">啟用&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled checked><span class="slider round"></span>
                        </label>
                    </a>
                    <a onclick="multiDisable();">
                        <p class="mb-0">停用&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled><span class="slider round"></span>
                        </label>
                    </a>
                    <a onclick="multiDelete();" class="mt-2 border-top">
                        刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                </div>
            </div>
        </div>

        <!-- 中心列表表格 -->
        <div class="edit_form">
            <table class="table-rwd table table-striped" style="min-width:1200px;">
                <thead>
                    <tr>
                        <th style="width: 20px">
                            <input type="checkbox" class="centerCheckboxAll"
                                   onclick="$('.table input[class=centerCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"
                                   style="width:100%; cursor:pointer;">
                        </th>
                        <th>ID</th>
                        <th>中心名稱</th>
                        <th>中心等級</th>
                        <th>現役人員數</th>
                        <th>建立時間</th>
                        <th>狀態</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    @if($data['centers']->isEmpty())
                        <tr><td colspan="8">沒有數據</td></tr>
                    @else
                        @foreach($data['centers'] as $center)
                            <tr id="center_{{ $center->id }}">
                                <td>
                                    <input type="checkbox" class="centerCheckbox" alt="{{ $center->id }}">
                                </td>

                                <td>{{ $center->id }}</td>
                                <td>
                                    {{ $center->name }}
                                </td>
                                <td>{{ $center->level->name ?? '未設定' }}</td>
                                <td>
                                    <span class="badge badge-info">
                                        {{ $center->active_staff_count ?? 0 }} 人
                                    </span>
                                </td>
                                <td>{{ $center->created_at ? $center->created_at->format('Y-m-d H:i') : '' }}</td>
                                <td>
                                    <label class="switch" style="display:inline-flex; margin-top: 5px;">
                                        <input type="checkbox"
                                               {{ $center->status == 1 ? 'checked' : '' }}
                                               onchange="toggleStatus({{ $center->id }}, this.checked)">
                                        <span class="slider round"></span>
                                    </label>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/order/center/edit?id={{ $center->id }}"
                                           class="btn btn-sm btn-warning" title="編輯">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-danger"
                                                title="刪除"
                                                onclick="deleteCenter({{ $center->id }}, '{{ addslashes($center->name) }}', {{ $center->active_staff_count ?? 0 }})">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>

        <!-- 分頁 -->
        <div class="text-center">
            {{ $data['centers']->appends(request()->query())->links('pagination::default') }}
        </div>
    </div>

    <!-- 刪除確認對話框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">確認刪除</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p id="deleteMessage"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDelete">確認刪除</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('ownCSS')
    {{-- Bootstrap Icons --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    {{-- UX 增強樣式 --}}
    <link rel="stylesheet" href="/static/admin/css/center-ux-enhancements.css">
    <style>
        /* 載入狀態樣式 */
        .updating-status {
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .updating-status td {
            position: relative;
        }

        /* 通知樣式 */
        .page-notification {
            border: none;
            border-left: 4px solid;
            animation: slideInRight 0.3s ease;
        }

        .page-notification.alert-success {
            border-left-color: #28a745;
        }

        .page-notification.alert-danger {
            border-left-color: #dc3545;
        }

        .page-notification.alert-warning {
            border-left-color: #ffc107;
        }

        .page-notification.alert-info {
            border-left-color: #17a2b8;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 表格行懸停效果 */
        .table-striped tbody tr:hover {
            background-color: rgba(0,123,255,0.05);
        }

        /* 操作按鈕組樣式 */
        .btn-group .btn {
            margin-right: 2px;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }

        /* 狀態開關樣式增強 */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: #28a745;
        }

        input:focus + .slider {
            box-shadow: 0 0 1px #28a745;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .slider.round {
            border-radius: 24px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        /* 禁用狀態 */
        input:disabled + .slider {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* 響應式調整 */
        @media (max-width: 768px) {
            .page-notification {
                left: 10px;
                right: 10px;
                min-width: auto;
                max-width: none;
            }

            .btn-group {
                display: flex;
                flex-wrap: wrap;
            }

            .btn-group .btn {
                margin-bottom: 2px;
            }
        }
    </style>
@endsection

@section('ownJS')
    {{-- UX 增強系統 --}}
    <script src="/static/admin/js/center-ux-enhancements.js"></script>
    {{-- 效能優化系統 --}}
    <script src="/static/admin/js/center-performance.js"></script>
    <script>
        $(function() {
            // 點擊其他地方隱藏編輯選單
            $(document).click(function() {
                $('.edit-item').fadeOut();
            });
            $('.edit').click(function(event) {
                event.stopPropagation();
            });
        });

        // 切換中心狀態
        function toggleStatus(centerId, status) {
            const $switch = $(`#center_${centerId} input[type="checkbox"]`);
            const $row = $(`#center_${centerId}`);

            // 顯示載入狀態
            $switch.prop('disabled', true);
            $row.addClass('updating-status');

            $.ajax({
                url: '/order/center/toggle-status',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    id: centerId,
                    status: status ? 1 : 0
                },
                timeout: 10000,
                success: function(response) {
                    if (response.status) {
                        // 成功更新，顯示提示
                        showNotification(
                            `中心狀態已${status ? '啟用' : '停用'}`,
                            'success'
                        );
                    } else {
                        // 更新失敗，恢復原狀態
                        $switch.prop('checked', !status);
                        showNotification('狀態更新失敗：' + response.message, 'error');
                    }
                },
                error: function(xhr) {
                    // 更新失敗，恢復原狀態
                    $switch.prop('checked', !status);

                    let errorMessage = '狀態更新失敗';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage += '：' + xhr.responseJSON.message;
                    } else if (xhr.status === 0) {
                        errorMessage += '：網路連線失敗';
                    } else if (xhr.status >= 500) {
                        errorMessage += '：伺服器錯誤';
                    }

                    showNotification(errorMessage, 'error');
                },
                complete: function() {
                    // 恢復開關狀態
                    $switch.prop('disabled', false);
                    $row.removeClass('updating-status');
                }
            });
        }

        // 刪除中心
        function deleteCenter(centerId, centerName, staffCount) {
            // 實作刪除前的安全檢查（檢查是否有現役人員）
            if (staffCount > 0) {
                let message = `中心「${centerName}」目前有 ${staffCount} 位現役人員，無法刪除。\n\n請先移除所有現役人員後再進行刪除操作。`;
                alert(message);
                return;
            }

            // 實作刪除確認對話框
            let message = `確定要刪除中心「${centerName}」嗎？\n\n此操作無法復原，請謹慎操作。`;
            $('#deleteMessage').html(message.replace(/\n/g, '<br>'));
            $('#deleteModal').modal('show');

            // 綁定確認刪除事件
            $('#confirmDelete').off('click').on('click', function() {
                // 顯示載入狀態
                $(this).prop('disabled', true).text('刪除中...');

                // 實作刪除操作和錯誤處理
                $.ajax({
                    url: '/order/center/destroy',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        id: centerId
                    },
                    success: function(response) {
                        if (response.status) {
                            alert('中心刪除成功');
                            // 移除表格行而不是重新載入整頁
                            $('#center_' + centerId).fadeOut(300, function() {
                                $(this).remove();
                            });
                        } else {
                            alert('刪除失敗：' + response.message);
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = '刪除失敗';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = '刪除失敗：' + xhr.responseJSON.message;
                        } else if (xhr.status === 409) {
                            errorMessage = '刪除失敗：中心仍有現役人員，請先移除所有現役人員';
                        } else if (xhr.status === 404) {
                            errorMessage = '刪除失敗：指定的中心不存在';
                        } else if (xhr.status === 500) {
                            errorMessage = '刪除失敗：系統錯誤，請稍後再試';
                        }

                        alert(errorMessage);
                    },
                    complete: function() {
                        // 恢復按鈕狀態
                        $('#confirmDelete').prop('disabled', false).text('確認刪除');
                        $('#deleteModal').modal('hide');
                    }
                });
            });
        }

        // 取得選中的中心ID
        function getSelectedIds() {
            var selectedIds = [];
            $('.centerCheckbox:checked').each(function() {
                selectedIds.push($(this).attr('alt'));
            });
            return selectedIds;
        }

        // 批量啟用
        function multiEnable() {
            var selectedIds = getSelectedIds();
            if (selectedIds.length === 0) {
                alert('請選擇要啟用的中心');
                return;
            }

            if (confirm(`確定要啟用選中的 ${selectedIds.length} 個中心嗎？`)) {
                $.ajax({
                    url: '/order/center/batch-status',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        ids: selectedIds,
                        status: 1
                    },
                    success: function(response) {
                        if (response.status) {
                            alert('批量啟用成功');
                            location.reload();
                        } else {
                            alert('批量啟用失敗：' + response.message);
                        }
                    },
                    error: function() {
                        alert('批量啟用失敗');
                    }
                });
            }

            // 取消全選
            $('.centerCheckboxAll').prop('checked', false);
        }

        // 批量停用
        function multiDisable() {
            var selectedIds = getSelectedIds();
            if (selectedIds.length === 0) {
                alert('請選擇要停用的中心');
                return;
            }

            if (confirm(`確定要停用選中的 ${selectedIds.length} 個中心嗎？`)) {
                $.ajax({
                    url: '/order/center/batch-status',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        ids: selectedIds,
                        status: 0
                    },
                    success: function(response) {
                        if (response.status) {
                            alert('批量停用成功');
                            location.reload();
                        } else {
                            alert('批量停用失敗：' + response.message);
                        }
                    },
                    error: function() {
                        alert('批量停用失敗');
                    }
                });
            }

            // 取消全選
            $('.centerCheckboxAll').prop('checked', false);
        }

        // 批量刪除
        function multiDelete() {
            var selectedIds = getSelectedIds();
            if (selectedIds.length === 0) {
                alert('請選擇要刪除的中心');
                return;
            }

            // 實作刪除確認對話框
            let confirmMessage = `確定要刪除選中的 ${selectedIds.length} 個中心嗎？\n\n注意：\n• 有現役人員的中心將無法刪除\n• 此操作無法復原，請謹慎操作`;

            if (confirm(confirmMessage)) {
                // 實作刪除操作和錯誤處理
                $.ajax({
                    url: '/order/center/batch-delete',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        ids: selectedIds
                    },
                    success: function(response) {
                        if (response.status) {
                            let message = response.message;

                            // 詳細顯示刪除結果
                            if (response.data) {
                                if (response.data.deleted_count > 0) {
                                    message += `\n\n成功刪除 ${response.data.deleted_count} 個中心`;
                                }

                                if (response.data.failed_count > 0) {
                                    message += `\n無法刪除 ${response.data.failed_count} 個中心：`;
                                    response.data.failed.forEach(function(failed) {
                                        if (failed.name) {
                                            message += `\n• ${failed.name}：${failed.reason}`;
                                        } else {
                                            message += `\n• 中心ID ${failed.id}：${failed.reason}`;
                                        }
                                    });
                                }
                            }

                            alert(message);

                            // 移除成功刪除的行
                            if (response.data && response.data.deleted) {
                                response.data.deleted.forEach(function(centerId) {
                                    $('#center_' + centerId).fadeOut(300, function() {
                                        $(this).remove();
                                    });
                                });
                            }

                            // 如果有失敗的項目，延遲後重新載入頁面以更新狀態
                            if (response.data && response.data.failed_count > 0) {
                                setTimeout(function() {
                                    location.reload();
                                }, 2000);
                            }
                        } else {
                            alert('批量刪除失敗：' + response.message);
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = '批量刪除失敗';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = '批量刪除失敗：' + xhr.responseJSON.message;
                        } else if (xhr.status === 422) {
                            errorMessage = '批量刪除失敗：請求參數無效';
                        } else if (xhr.status === 500) {
                            errorMessage = '批量刪除失敗：系統錯誤，請稍後再試';
                        }

                        alert(errorMessage);
                    }
                });
            }

            // 取消全選
            $('.centerCheckboxAll').prop('checked', false);
        }

        // Show/Hide 函數（用於編輯選單）
        function Show(selector) {
            $(selector).toggle();
        }

        // 通知函數
        function showNotification(message, type = 'info', duration = 5000) {
            // 移除現有通知
            $('.page-notification').remove();

            const typeClasses = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            };

            const typeIcons = {
                'success': 'bi-check-circle',
                'error': 'bi-exclamation-triangle',
                'warning': 'bi-exclamation-circle',
                'info': 'bi-info-circle'
            };

            const notification = $(`
                <div class="page-notification alert ${typeClasses[type]} alert-dismissible fade show"
                     style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                    <i class="${typeIcons[type]} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" onclick="$(this).closest('.alert').remove()"></button>
                </div>
            `);

            $('body').append(notification);

            // 自動隱藏
            if (duration > 0) {
                setTimeout(function() {
                    notification.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, duration);
            }
        }

        // 顯示載入狀態
        function showPageLoading(show, message = '處理中...') {
            if (show) {
                if ($('.page-loading-overlay').length === 0) {
                    const loadingHtml = `
                        <div class="page-loading-overlay" style="
                            position: fixed;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: rgba(255, 255, 255, 0.8);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            z-index: 10000;
                        ">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                                    <span class="sr-only">載入中...</span>
                                </div>
                                <div class="text-muted">${message}</div>
                            </div>
                        </div>
                    `;
                    $('body').append(loadingHtml);
                }
            } else {
                $('.page-loading-overlay').remove();
            }
        }

        // 處理 AJAX 錯誤
        function handleAjaxError(xhr, defaultMessage = '操作失敗') {
            let errorMessage = defaultMessage;

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 0) {
                errorMessage = '網路連線失敗，請檢查網路狀態';
            } else if (xhr.status === 404) {
                errorMessage = '請求的資源不存在';
            } else if (xhr.status === 403) {
                errorMessage = '沒有權限執行此操作';
            } else if (xhr.status === 422) {
                errorMessage = '提交的資料格式不正確';
            } else if (xhr.status >= 500) {
                errorMessage = '伺服器內部錯誤，請稍後再試';
            }

            showNotification(errorMessage, 'error');
            return errorMessage;
        }

        // 增強的確認對話框
        function showConfirmDialog(title, message, onConfirm, onCancel = null) {
            const dialogHtml = `
                <div class="modal fade" id="customConfirmModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <p>${message.replace(/\n/g, '<br>')}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" id="customConfirmBtn">確認</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除現有對話框
            $('#customConfirmModal').remove();

            // 添加新對話框
            $('body').append(dialogHtml);

            // 綁定事件
            $('#customConfirmBtn').on('click', function() {
                $('#customConfirmModal').modal('hide');
                if (typeof onConfirm === 'function') {
                    onConfirm();
                }
            });

            $('#customConfirmModal').on('hidden.bs.modal', function() {
                $(this).remove();
                if (typeof onCancel === 'function') {
                    onCancel();
                }
            });

            // 顯示對話框
            $('#customConfirmModal').modal('show');
        }
    </script>
@endsection
